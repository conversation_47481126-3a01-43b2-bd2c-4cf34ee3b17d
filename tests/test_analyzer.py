#!/usr/bin/env python3
"""
Tests for Azure Monitoring Data Analyzer
"""

import pytest
import json
import tempfile
import zipfile
from pathlib import Path
import sys
import os

# Add src to path for testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from azure_analysis.analyzer import AzureMonitoringAnalyzer


class TestAzureMonitoringAnalyzer:
    """Test cases for the Azure Monitoring Analyzer."""
    
    def test_analyzer_initialization(self):
        """Test analyzer can be initialized."""
        analyzer = AzureMonitoringAnalyzer()
        assert analyzer is not None
        assert analyzer.config == {}
    
    def test_config_loading(self):
        """Test configuration loading."""
        config_data = {
            "analysis": {
                "input_zip_path": "test.zip",
                "output_csv_path": "test.csv"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_path = f.name
        
        try:
            analyzer = AzureMonitoringAnalyzer(config_path)
            assert analyzer.config == config_data
        finally:
            os.unlink(config_path)
    
    def test_metric_unit_detection(self):
        """Test metric unit detection."""
        analyzer = AzureMonitoringAnalyzer()
        
        assert analyzer._get_metric_unit("cpu_percent") == "%"
        assert analyzer._get_metric_unit("memory_bytes") == "Bytes"
        assert analyzer._get_metric_unit("active_connections") == "Count"
        assert analyzer._get_metric_unit("read_iops") == "IOPS"
        assert analyzer._get_metric_unit("network_throughput") == "Bytes/sec"
        assert analyzer._get_metric_unit("response_time") == "Seconds"
        assert analyzer._get_metric_unit("unknown_metric") == "Value"


if __name__ == "__main__":
    pytest.main([__file__])
