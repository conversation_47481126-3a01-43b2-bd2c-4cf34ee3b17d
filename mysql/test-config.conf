# Azure Configuration File
# Copy this file and update with your actual values

# Azure Subscription ID
AZURE_SUBSCRIPTION_ID="207a84e2-208b-4809-baab-e94a043914f5"

# Resource Group Name
RESOURCE_GROUP_NAME="amit-test"


SERVER_NAME="amit-mysqldb2"

# Monitoring Configuration
# METRICS_TIME_PERIOD: Time period for metrics collection
# Supported values: 1h, 6h, 12h, 1d, 3d, 7d, 30d
# Examples: "1h" = 1 hour, "1d" = 1 day, "7d" = 7 days
METRICS_TIME_PERIOD="1h"

# Server Parameters Configuration
# FETCH_PARAMETERS: Whether to fetch MySQL server parameters
# Set to "true" to fetch server parameters, "false" to skip
# Default: false (to keep script execution faster)
FETCH_PARAMETERS="false"

# =============================================================================
# METRICS COLLECTION CONFIGURATION
# =============================================================================
# Generic metrics collection using comma-separated list
# When Azure changes metric names, just update METRICS_LIST - no script changes needed!

# Enable/Disable Metrics Collection
COLLECT_METRICS="false"  # Master switch for all metrics collection

# =============================================================================
# METRICS LIST - Update this when Azure changes metric names
# =============================================================================
# Comma-separated list of Azure metric names to collect
# Script will attempt to collect each metric, skip if not available
# No script changes needed when Azure updates metric names!

METRICS_LIST="cpu_percent,memory_percent,active_connections,io_consumption_percent,network_bytes_ingress,replication_lag,backup_storage_used,serverlog_storage_usage,aborted_connections,total_connections,Slow_queries,Queries,Com_select,Com_insert,Threads_running,storage_limit,storage_used,data_storage_used,binlog_storage_used,serverlog_storage_percent,Innodb_buffer_pool_reads,Innodb_buffer_pool_read_requests,Innodb_data_writes,Uptime,active_transactions"
