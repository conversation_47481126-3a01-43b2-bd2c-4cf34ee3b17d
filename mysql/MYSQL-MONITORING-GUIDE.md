# Azure MySQL Flexible Server Monitoring & Replica Discovery Architecture

## Overview

This system provides comprehensive monitoring, metrics collection, and replica topology discovery for Azure MySQL Flexible Server instances. It automatically detects replication relationships, collects performance metrics, and organizes data in a structured format for analysis and compliance.

## System Architecture

```mermaid
graph TB
    subgraph "Input Layer"
        A[Configuration File] --> B[Script Entry Point]
        B --> C[Validation Engine]
    end
    
    subgraph "Discovery Layer"
        C --> D[Azure Authentication]
        D --> E[Resource Group Validation]
        E --> F[Server Discovery]
        F --> G[Replica Topology Discovery]
    end
    
    subgraph "Collection Layer"
        G --> H[Primary Server Metrics]
        G --> I[Replica Server Metrics]
        H --> J[Performance Data]
        I --> K[Replication Data]
        J --> L[Server Parameters]
        K --> L
    end
    
    subgraph "Organization Layer"
        L --> M[File Organization Engine]
        M --> N[Primary Server Directory]
        M --> O[Replica Node Directories]
        N --> P[Summary Generation]
        O --> P
    end
    
    subgraph "Output Layer"
        P --> Q[Structured JSON Files]
        P --> R[Metrics Reports]
        P --> S[Topology Maps]
    end
```

## Core Components

### 1. Configuration Management
- **File**: `test-config.conf`
- **Purpose**: Centralized configuration for Azure credentials, server details, and monitoring parameters
- **Format**: Shell environment variables

```bash
AZURE_SUBSCRIPTION_ID="subscription-id"
RESOURCE_GROUP_NAME="resource-group"
SERVER_NAME="server-name"
METRICS_TIME_PERIOD="1h"
FETCH_PARAMETERS="true"
```

### 2. Replica Discovery Engine

#### Discovery Algorithm
```mermaid
flowchart TD
    A[List All Servers in RG] --> B[Analyze sourceServerResourceId]
    B --> C{Current Server Role?}
    C -->|Primary| D[Find Replicas OF This Server]
    C -->|Replica| E[Find Source Server]
    C -->|Standalone| F[No Replication]
    D --> G[Collect Replica Metrics]
    E --> H[Document Replica Status]
    F --> I[Single Server Mode]
    G --> J[Create Topology Map]
    H --> J
    I --> J
```

#### Key Properties Analyzed
- `sourceServerResourceId`: Points to primary server (null for primary/standalone)
- `replicationRole`: "Primary", "Replica", or "None"
- `replicaCapacity`: Maximum number of replicas supported

### 3. Metrics Collection System

#### Primary Server Metrics
- **CPU Utilization**: `cpu_percent`
- **Memory Usage**: `memory_percent`
- **Storage Usage**: `storage_percent`
- **Active Connections**: `active_connections`
- **IO Consumption**: `io_consumption_percent`
- **Network Traffic**: `network_bytes_ingress`, `network_bytes_egress`
- **Query Performance**: `queries`
- **Connection Issues**: `aborted_connections`

#### Replica-Specific Metrics
- **Replication Lag**: `replication_lag` (critical for DR planning)
- **CPU Usage**: Performance comparison with primary
- **Connection Status**: Replica availability monitoring

### 4. File Organization Architecture

```
mysql-flexible-server-{SERVER_NAME}_{TIMESTAMP}/
├── {SERVER_NAME}_replication_info.json     # Topology overview
├── summary.json                             # Consolidated metrics
├── primary-server/                          # Primary server data
│   ├── {SERVER_NAME}_full_details.json
│   ├── {SERVER_NAME}_cpu_metrics.json
│   ├── {SERVER_NAME}_memory_metrics.json
│   ├── {SERVER_NAME}_storage_metrics.json
│   ├── {SERVER_NAME}_connection_metrics.json
│   ├── {SERVER_NAME}_io_metrics.json
│   ├── {SERVER_NAME}_network_in_metrics.json
│   ├── {SERVER_NAME}_network_out_metrics.json
│   ├── {SERVER_NAME}_replication_lag_metrics.json
│   ├── {SERVER_NAME}_aborted_connections_metrics.json
│   ├── {SERVER_NAME}_queries_metrics.json
│   └── {SERVER_NAME}_parameters.json
└── replica-node-{N}/                       # Per-replica directories
    ├── {REPLICA_NAME}_replica_details.json
    ├── {REPLICA_NAME}_metrics_summary.json
    ├── {REPLICA_NAME}_cpu_metrics.json
    ├── {REPLICA_NAME}_replication_lag_metrics.json
    └── replica_info.json                   # Replica metadata
```

## Data Flow Architecture

### 1. Authentication & Validation Flow
```mermaid
sequenceDiagram
    participant U as User
    participant S as Script
    participant A as Azure CLI
    participant R as Resource Group
    
    U->>S: Execute with config
    S->>S: Validate configuration
    S->>A: Check authentication
    A-->>S: Auth status
    S->>R: Validate resource group
    R-->>S: RG details
    S->>S: Proceed to discovery
```

### 2. Replica Discovery Flow
```mermaid
sequenceDiagram
    participant S as Script
    participant A as Azure API
    participant D as Discovery Engine
    participant F as File System
    
    S->>A: List all servers in RG
    A-->>S: Server list with metadata
    S->>D: Analyze replication relationships
    D->>D: Parse sourceServerResourceId
    D->>D: Identify server roles
    D->>A: Get replica details
    A-->>D: Replica configurations
    D->>F: Create topology map
```

### 3. Metrics Collection Flow
```mermaid
sequenceDiagram
    participant S as Script
    participant M as Azure Monitor
    participant P as Primary Server
    participant R as Replica Servers
    participant F as File System
    
    S->>M: Request primary metrics
    M->>P: Collect performance data
    P-->>M: Metrics response
    M-->>S: Primary metrics JSON
    S->>M: Request replica metrics
    M->>R: Collect replica data
    R-->>M: Replica metrics
    M-->>S: Replica metrics JSON
    S->>F: Organize all data
```

## Key Design Patterns

### 1. Modular Function Architecture
- **Single Responsibility**: Each function handles one specific task
- **Error Handling**: Comprehensive error checking and graceful degradation
- **Logging**: Detailed progress tracking and debugging information

### 2. Data Organization Strategy
- **Hierarchical Structure**: Clear separation between primary and replica data
- **Scalable Design**: Supports unlimited number of replicas
- **Metadata Rich**: Each component includes comprehensive metadata

### 3. Configuration-Driven Approach
- **Flexible Parameters**: Time periods, feature toggles, and server selection
- **Environment Agnostic**: Works across different Azure subscriptions and regions
- **Validation First**: Comprehensive input validation before execution

## Integration Points

### Azure Services
- **Azure CLI**: Primary interface for Azure resource management
- **Azure Monitor**: Metrics collection and performance monitoring
- **Azure MySQL Flexible Server**: Target service for monitoring

### External Dependencies
- **jq**: JSON parsing and manipulation
- **bash**: Shell scripting environment
- **date**: Time calculation and formatting

## Security Considerations

### Authentication
- Leverages existing Azure CLI authentication
- No credential storage in configuration files
- Subscription-level access validation

### Data Handling
- Read-only operations on Azure resources
- Local file system storage only
- No sensitive data exposure in logs

## Performance Characteristics

### Scalability
- **Replica Count**: Unlimited replica discovery and monitoring
- **Time Periods**: Configurable from 1 hour to 30 days
- **Parallel Processing**: Concurrent metrics collection where possible

### Efficiency
- **Incremental Collection**: Only collects new data
- **Selective Metrics**: Configurable parameter collection
- **Optimized Queries**: Efficient Azure API usage

## Error Handling Strategy

### Graceful Degradation
- Continues processing if individual metrics fail
- Warns about missing optional data
- Maintains partial results for analysis

### Comprehensive Logging
- Progress tracking for long-running operations
- Detailed error messages with context
- Success confirmation for each major step

## Future Extensibility

### Planned Enhancements
- **Cross-Region Replica Support**: Enhanced discovery across regions
- **Historical Trend Analysis**: Long-term metrics storage and analysis
- **Automated Alerting**: Threshold-based monitoring alerts
- **Export Formats**: CSV, Excel, and dashboard integration

### Architecture Flexibility
- **Plugin System**: Modular metric collectors
- **Custom Outputs**: Configurable report formats
- **Integration APIs**: REST endpoints for external systems

## Deployment Architecture

### System Requirements
```mermaid
graph LR
    subgraph "Runtime Environment"
        A[Bash 4.0+] --> B[Azure CLI 2.0+]
        B --> C[jq 1.6+]
        C --> D[Standard Unix Tools]
    end

    subgraph "Azure Prerequisites"
        E[Valid Azure Subscription] --> F[Resource Group Access]
        F --> G[MySQL Flexible Server]
        G --> H[Monitor Reader Role]
    end

    subgraph "Network Requirements"
        I[Internet Connectivity] --> J[Azure API Access]
        J --> K[HTTPS/443 Outbound]
    end
```

### Execution Environments
- **Local Development**: Developer workstations with Azure CLI
- **CI/CD Pipelines**: Automated monitoring in build systems
- **Azure Cloud Shell**: Browser-based execution environment
- **Container Deployment**: Docker-based monitoring solutions

## Data Models

### Server Configuration Model
```json
{
  "server_summary": {
    "name": "string",
    "status": "Ready|Updating|Disabled",
    "location": "string",
    "mysql_version": "string",
    "fqdn": "string",
    "pricing_tier": "Burstable|GeneralPurpose|MemoryOptimized",
    "sku_name": "string",
    "vcpu_capacity": "number",
    "storage_size_gb": "number",
    "storage_iops": "number",
    "backup_retention_days": "number"
  }
}
```

### Replication Topology Model
```json
{
  "server_role": "primary|replica|standalone",
  "server_name": "string",
  "source_server": "string|null",
  "source_server_resource_id": "string|null",
  "replica_servers": ["string"],
  "replica_count": "number"
}
```

### Metrics Data Model
```json
{
  "metric_name": "string",
  "time_period": "string",
  "aggregation": "Average|Maximum|Minimum|Total",
  "data_points": [
    {
      "timestamp": "ISO8601",
      "value": "number",
      "unit": "string"
    }
  ],
  "summary": {
    "average": "number",
    "maximum": "number",
    "minimum": "number"
  }
}
```

## Monitoring & Observability

### Script Execution Monitoring
- **Progress Tracking**: Real-time status updates
- **Performance Metrics**: Execution time per component
- **Error Rates**: Failed operations tracking
- **Resource Usage**: Azure API call optimization

### Data Quality Assurance
- **Completeness Checks**: Verify all expected metrics collected
- **Validation Rules**: Data consistency and format verification
- **Anomaly Detection**: Unusual metric values flagging
- **Audit Trail**: Complete operation logging

## Troubleshooting Guide

### Common Issues & Solutions

#### Authentication Problems
```bash
# Issue: Azure CLI not authenticated
# Solution: Re-authenticate
az login
az account set --subscription "subscription-id"
```

#### Missing Metrics
```bash
# Issue: Some metrics return "No data available"
# Cause: Metric not enabled or insufficient time period
# Solution: Check metric availability and extend time period
```

#### Replica Discovery Failures
```bash
# Issue: Replicas not detected
# Cause: Insufficient permissions or cross-region replicas
# Solution: Verify Reader role on all resource groups
```

### Debug Mode
```bash
# Enable verbose logging
export DEBUG=true
./azure_mysql_flexible_server_check.sh config.conf
```

## Compliance & Governance

### Data Governance
- **Data Retention**: Configurable local storage policies
- **Access Control**: Azure RBAC integration
- **Audit Logging**: Complete operation tracking
- **Data Classification**: Automatic PII detection and handling

### Compliance Standards
- **SOC 2**: Monitoring and logging requirements
- **ISO 27001**: Information security management
- **GDPR**: Data protection and privacy compliance
- **HIPAA**: Healthcare data security (where applicable)

## Performance Optimization

### Execution Optimization
- **Parallel Processing**: Concurrent metric collection
- **Caching Strategy**: Temporary data storage for efficiency
- **Batch Operations**: Grouped Azure API calls
- **Resource Pooling**: Connection reuse optimization

### Storage Optimization
- **Compression**: JSON file compression options
- **Archival**: Automated old data cleanup
- **Indexing**: Fast data retrieval mechanisms
- **Partitioning**: Time-based data organization

## Disaster Recovery

### Data Backup Strategy
- **Local Backups**: Automated local file backups
- **Cloud Storage**: Azure Blob Storage integration
- **Version Control**: Git-based configuration management
- **Recovery Procedures**: Documented restoration processes

### Business Continuity
- **Failover Procedures**: Alternative execution environments
- **Data Recovery**: Point-in-time restoration capabilities
- **Service Continuity**: Monitoring during Azure outages
- **Communication Plans**: Stakeholder notification procedures
