#!/bin/bash
# Example of config-driven metrics collection for MySQL Flexible Server

# Function to collect a single metric and save to JSON file
collect_metric() {
    local resource_id="$1"
    local metric_name="$2"
    local start_time="$3"
    local end_time="$4"
    local display_name="${5:-$metric_name}"
    local aggregation="${6:-Average}"
    local interval="${7:-PT1H}"
    
    log_info "Collecting metric: $display_name ($metric_name)"
    
    # Execute Azure CLI command to get metric
    local metric_data=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "$metric_name" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval "$interval" \
        --aggregation "$aggregation" \
        --output json 2>/dev/null)
    
    # Save raw metric data to JSON file
    local output_file="${SERVER_NAME}_${metric_name}_metrics.json"
    echo "$metric_data" > "$output_file"
    
    # Return success/failure
    if [[ -f "$output_file" && -s "$output_file" ]]; then
        return 0
    else
        return 1
    fi
}

# Function to get monitoring metrics
get_monitoring_metrics() {
    log_info "Starting monitoring metrics collection..."

    local time_period_display=""
    case "$METRICS_TIME_PERIOD" in
        "1h") time_period_display="last 1 hour" ;;
        "6h") time_period_display="last 6 hours" ;;
        "12h") time_period_display="last 12 hours" ;;
        "1d") time_period_display="last 1 day" ;;
        "3d") time_period_display="last 3 days" ;;
        "7d") time_period_display="last 7 days" ;;
        "30d") time_period_display="last 30 days" ;;
        *) time_period_display="last 1 hour" ;;
    esac

    log_info "Fetching monitoring metrics ($time_period_display)..."

    # Get current time and calculate start time based on configured period
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")

    log_info "Time range: $hours_ago hours ago to now"

    # Calculate start time (compatible with both GNU date and BSD date)
    local start_time
    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        # GNU date (Linux)
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
        log_info "Using GNU date format"
    else
        # BSD date (macOS)
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
        log_info "Using BSD date format"
    fi

    log_info "Start time: $start_time"
    log_info "End time: $end_time"

    # Get resource ID for metrics
    log_info "Getting resource ID for metrics..."
    local resource_id=$(az mysql flexible-server show --resource-group "$RESOURCE_GROUP_NAME" --name "$SERVER_NAME" --query "id" -o tsv)

    if [[ -z "$resource_id" ]]; then
        log_error "Failed to get resource ID for metrics"
        return 1
    fi

    log_info "Resource ID: $resource_id"

    echo ""
    echo "=================================================="
    echo "           COLLECTING METRICS ($(echo "$time_period_display" | tr '[:lower:]' '[:upper:]'))"
    echo "=================================================="
    
    # Collect metrics based on configuration
    local metrics_collected=0
    local metrics_failed=0
    
    # Create a metrics collection summary file
    local metrics_summary_file="${SERVER_NAME}_metrics_summary.json"
    echo "{" > "$metrics_summary_file"
    echo "  \"server_name\": \"$SERVER_NAME\"," >> "$metrics_summary_file"
    echo "  \"collection_timestamp\": \"$(date -u +"%Y-%m-%d %H:%M:%S UTC")\"," >> "$metrics_summary_file"
    echo "  \"time_period\": \"$METRICS_TIME_PERIOD\"," >> "$metrics_summary_file"
    echo "  \"start_time\": \"$start_time\"," >> "$metrics_summary_file"
    echo "  \"end_time\": \"$end_time\"," >> "$metrics_summary_file"
    echo "  \"metrics_collected\": {" >> "$metrics_summary_file"
    
    # Core Performance Metrics
    if [[ "$COLLECT_CPU_METRICS" == "true" ]]; then
        if collect_metric "$resource_id" "$METRIC_CPU_PERCENT" "$start_time" "$end_time" "CPU Utilization"; then
            echo "    \"cpu_metrics\": \"${SERVER_NAME}_${METRIC_CPU_PERCENT}_metrics.json\"," >> "$metrics_summary_file"
            ((metrics_collected++))
            log_success "✓ CPU metrics collected"
        else
            log_warning "⚠ Failed to collect CPU metrics"
            ((metrics_failed++))
        fi
    fi
    
    if [[ "$COLLECT_MEMORY_METRICS" == "true" ]]; then
        if collect_metric "$resource_id" "$METRIC_MEMORY_PERCENT" "$start_time" "$end_time" "Memory Utilization"; then
            echo "    \"memory_metrics\": \"${SERVER_NAME}_${METRIC_MEMORY_PERCENT}_metrics.json\"," >> "$metrics_summary_file"
            ((metrics_collected++))
            log_success "✓ Memory metrics collected"
        else
            log_warning "⚠ Failed to collect memory metrics"
            ((metrics_failed++))
        fi
    fi
    
    if [[ "$COLLECT_STORAGE_METRICS" == "true" ]]; then
        if collect_metric "$resource_id" "$METRIC_STORAGE_PERCENT" "$start_time" "$end_time" "Storage Utilization"; then
            echo "    \"storage_metrics\": \"${SERVER_NAME}_${METRIC_STORAGE_PERCENT}_metrics.json\"," >> "$metrics_summary_file"
            ((metrics_collected++))
            log_success "✓ Storage metrics collected"
        else
            log_warning "⚠ Failed to collect storage metrics"
            ((metrics_failed++))
        fi
    fi
    
    # Finalize metrics summary file
    # Remove trailing comma if present
    sed -i.bak '$ s/,$//' "$metrics_summary_file"
    rm -f "${metrics_summary_file}.bak"
    
    # Close the JSON object
    echo "  }," >> "$metrics_summary_file"
    echo "  \"metrics_collected_count\": $metrics_collected," >> "$metrics_summary_file"
    echo "  \"metrics_failed_count\": $metrics_failed" >> "$metrics_summary_file"
    echo "}" >> "$metrics_summary_file"
    
    log_info "Metrics collection summary saved to: $metrics_summary_file"
    log_success "Successfully collected $metrics_collected metrics (Failed: $metrics_failed)"
    
    echo "=================================================="
    log_success "All monitoring metrics saved to individual JSON files"
}
