#!/bin/bash

# Azure MySQL Flexible Server Monitoring and Replica Discovery Script
# Version: 1.0
# Description: Comprehensive monitoring, metrics collection, and replica topology discovery
#              for Azure MySQL Flexible Server instances
#
# USAGE:
#   bash azure_mysql_flexible_server_check.sh --config-path <config-file> [--generate-zip <true|false>]
#
# EXAMPLES:
#   # JSON configuration (recommended) - ZIP generated by default
#   bash azure_mysql_flexible_server_check.sh --config-path config.json
#
#   # Legacy .conf configuration - ZIP generated by default
#   bash azure_mysql_flexible_server_check.sh --config-path config.conf
#
#   # Disable ZIP generation (keep directory structure)
#   bash azure_mysql_flexible_server_check.sh --config-path config.json --generate-zip false
#
# JSON CONFIGURATION FORMAT:
#   {
#     "azure": {
#       "subscription_id": "your-subscription-id",
#       "resource_group": "your-resource-group",
#       "server_name": "your-mysql-server"
#     },
#     "monitoring": {
#       "metrics_time_period": "1h"
#     },
#     "collection": {
#       "fetch_parameters": true,
#       "collect_metrics": true,
#       "metrics_list": ["cpu_percent", "memory_percent", "active_connections"]
#     }
#   }
#
# LEGACY .CONF FORMAT (still supported):
#   AZURE_SUBSCRIPTION_ID="your-subscription-id"
#   RESOURCE_GROUP_NAME="your-resource-group"
#   SERVER_NAME="your-mysql-server"
#   METRICS_TIME_PERIOD="1h"
#   FETCH_PARAMETERS="true"
#   COLLECT_METRICS="true"

# Note: Removed -e to allow graceful error handling and continuation

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get timestamp with both local and UTC time
get_timestamp() {
    local local_time=$(date +"%Y-%m-%d %H:%M:%S")
    local utc_time=$(date -u +"%Y-%m-%d %H:%M:%S")
    echo "${local_time} (UTC: ${utc_time})"
}

# Function to print colored output with timestamps
log_info() {
    echo -e "${BLUE}[$(get_timestamp)] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(get_timestamp)] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(get_timestamp)] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(get_timestamp)] [ERROR]${NC} $1"
}

# Function to execute Azure CLI commands safely with error handling
safe_az_command() {
    local command_description="$1"
    shift
    local az_command=("$@")

    log_info "Executing: $command_description" >&2

    local output
    local exit_code

    # Execute the command and capture both output and exit code
    if output=$("${az_command[@]}" 2>&1); then
        exit_code=0
        echo "$output"
        return 0
    else
        exit_code=$?
        log_error "Failed: $command_description" >&2
        log_error "Command: ${az_command[*]}" >&2
        log_error "Error output: $output" >&2
        log_warning "Continuing with next operation..." >&2
        # Return empty output to avoid jq parse errors
        echo ""
        return $exit_code
    fi
}

# Function to check if required tools are installed
check_prerequisites() {
    local missing_tools=()

    # Check Azure CLI
    if ! command -v az &> /dev/null; then
        missing_tools+=("Azure CLI")
        log_error "Azure CLI is not installed."
        log_info "Install: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    fi

    # Check jq for JSON parsing
    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
        log_error "jq is not installed (required for JSON parsing)."
        log_info "Install jq:"
        log_info "  macOS: brew install jq"
        log_info "  Ubuntu/Debian: sudo apt-get install jq"
        log_info "  CentOS/RHEL: sudo yum install jq"
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi

    log_success "All required tools are available"
}

# Function to validate time period format
validate_time_period() {
    local time_period="$1"

    # Valid time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d
    case "$time_period" in
        "1h"|"6h"|"12h"|"1d"|"3d"|"7d"|"30d")
            log_success "Valid time period: $time_period"
            ;;
        *)
            log_error "Invalid time period: $time_period"
            log_info "Supported time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
            log_info "Examples: '1h' = 1 hour, '1d' = 1 day, '7d' = 7 days"
            exit 1
            ;;
    esac
}

# Function to convert time period to hours for date calculation
convert_time_period_to_hours() {
    local time_period="$1"

    case "$time_period" in
        "1h") echo "1" ;;
        "6h") echo "6" ;;
        "12h") echo "12" ;;
        "1d") echo "24" ;;
        "3d") echo "72" ;;
        "7d") echo "168" ;;
        "30d") echo "720" ;;
        *) echo "1" ;;  # Default to 1 hour
    esac
}

# Function to validate time period format
validate_time_period() {
    local time_period="$1"

    # Valid time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d
    case "$time_period" in
        "1h"|"6h"|"12h"|"1d"|"3d"|"7d"|"30d")
            log_success "Valid time period: $time_period"
            ;;
        *)
            log_error "Invalid time period: $time_period"
            log_info "Supported time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
            log_info "Examples: '1h' = 1 hour, '1d' = 1 day, '7d' = 7 days"
            exit 1
            ;;
    esac
}

# Function to convert time period to hours for date calculation
convert_time_period_to_hours() {
    local time_period="$1"

    case "$time_period" in
        "1h") echo "1" ;;
        "6h") echo "6" ;;
        "12h") echo "12" ;;
        "1d") echo "24" ;;
        "3d") echo "72" ;;
        "7d") echo "168" ;;
        "30d") echo "720" ;;
        *) echo "1" ;;  # Default to 1 hour
    esac
}

# Function to check metric thresholds and generate alerts
check_metric_threshold() {
    local metric_name="$1"
    local metric_value="$2"
    local threshold="$3"
    local unit="$4"

    if [[ -n "$threshold" && "$metric_value" != "N/A" ]]; then
        # Convert values to integers for comparison (remove decimal places)
        local metric_int=$(echo "$metric_value" | cut -d'.' -f1)
        local threshold_int=$(echo "$threshold" | cut -d'.' -f1)

        if [[ "$metric_int" -gt "$threshold_int" ]]; then
            log_warning "🚨 ALERT: $metric_name ($metric_value$unit) exceeds threshold ($threshold$unit)"
            return 1
        else
            log_success "✅ $metric_name ($metric_value$unit) is within threshold ($threshold$unit)"
            return 0
        fi
    fi
    return 0
}

# Function to load JSON configuration file
load_json_config() {
    local json_file="$1"

    # Check if jq is available
    if ! command -v jq >/dev/null 2>&1; then
        log_error "jq is required to parse JSON configuration files"
        log_info "Please install jq or use .conf format instead"
        exit 1
    fi

    # Validate JSON syntax
    if ! jq empty "$json_file" 2>/dev/null; then
        log_error "Invalid JSON syntax in config file: $json_file"
        exit 1
    fi

    # Extract configuration values from JSON
    AZURE_SUBSCRIPTION_ID=$(jq -r '.azure.subscription_id // empty' "$json_file")
    RESOURCE_GROUP_NAME=$(jq -r '.azure.resource_group // empty' "$json_file")
    SERVER_NAME=$(jq -r '.azure.server_name // empty' "$json_file")

    METRICS_TIME_PERIOD=$(jq -r '.monitoring.metrics_time_period // "1h"' "$json_file")

    FETCH_PARAMETERS=$(jq -r '.collection.fetch_parameters // false' "$json_file")
    COLLECT_METRICS=$(jq -r '.collection.collect_metrics // false' "$json_file")

    # Convert metrics_list array to comma-separated string
    local metrics_array=$(jq -r '.collection.metrics_list[]?' "$json_file" 2>/dev/null)
    if [[ -n "$metrics_array" ]]; then
        METRICS_LIST=$(echo "$metrics_array" | tr '\n' ',' | sed 's/,$//')
    else
        METRICS_LIST=""
    fi

    log_info "Successfully loaded JSON configuration"
}

# Function to validate config file and extract values
validate_config() {
    local config_file="$1"

    if [[ ! -f "$config_file" ]]; then
        log_error "Config file '$config_file' not found"
        exit 1
    fi

    log_info "Reading configuration from: $config_file"

    # Determine config file format and load accordingly
    if [[ "$config_file" == *.json ]]; then
        load_json_config "$config_file"
    else
        # Source the config file (legacy .conf format)
        source "$config_file"
    fi
    
    # Validate required variables
    if [[ -z "$AZURE_SUBSCRIPTION_ID" ]]; then
        log_error "AZURE_SUBSCRIPTION_ID not found in config file"
        exit 1
    fi

    if [[ -z "$RESOURCE_GROUP_NAME" ]]; then
        log_error "RESOURCE_GROUP_NAME not found in config file"
        exit 1
    fi

    if [[ -z "$SERVER_NAME" ]]; then
        log_error "SERVER_NAME not found in config file"
        exit 1
    fi

    # Set default values for optional parameters
    METRICS_TIME_PERIOD="${METRICS_TIME_PERIOD:-1h}"
    FETCH_PARAMETERS="${FETCH_PARAMETERS:-false}"

    # Validate time period format
    validate_time_period "$METRICS_TIME_PERIOD"

    # Validate fetch parameters setting
    if [[ "$FETCH_PARAMETERS" != "true" && "$FETCH_PARAMETERS" != "false" ]]; then
        log_error "Invalid FETCH_PARAMETERS value: $FETCH_PARAMETERS"
        log_info "FETCH_PARAMETERS must be 'true' or 'false'"
        exit 1
    fi

    log_success "Configuration validated successfully"
    log_info "Subscription ID: $AZURE_SUBSCRIPTION_ID"
    log_info "Resource Group: $RESOURCE_GROUP_NAME"
    log_info "Server Name: $SERVER_NAME"
    log_info "Metrics Time Period: $METRICS_TIME_PERIOD"
    log_info "Fetch Parameters: $FETCH_PARAMETERS"
}

# Function to check current Azure login status
check_azure_login() {
    log_info "Checking Azure login status..."
    
    # Check if user is logged in
    if ! az account show &> /dev/null; then
        log_error "Not logged into Azure. Please run 'az login' first."
        exit 1
    fi
    
    # Get current subscription
    local current_subscription=$(az account show --query "id" -o tsv 2>/dev/null)
    
    if [[ -z "$current_subscription" ]]; then
        log_error "Unable to retrieve current subscription information"
        exit 1
    fi
    
    log_success "Currently logged into Azure"
    log_info "Current subscription: $current_subscription"
    
    # Check if the required subscription is available
    if ! az account show --subscription "$AZURE_SUBSCRIPTION_ID" &> /dev/null; then
        log_error "Subscription '$AZURE_SUBSCRIPTION_ID' is not available or accessible"
        log_info "Available subscriptions:"
        az account list --query "[].{Name:name, SubscriptionId:id, State:state}" -o table
        exit 1
    fi
    
    # Set the subscription if it's different from current
    if [[ "$current_subscription" != "$AZURE_SUBSCRIPTION_ID" ]]; then
        log_info "Switching to subscription: $AZURE_SUBSCRIPTION_ID"
        if az account set --subscription "$AZURE_SUBSCRIPTION_ID"; then
            log_success "Successfully switched to subscription: $AZURE_SUBSCRIPTION_ID"
        else
            log_error "Failed to switch to subscription: $AZURE_SUBSCRIPTION_ID"
            exit 1
        fi
    else
        log_success "Already using the correct subscription"
    fi
}

# Function to validate resource group
validate_resource_group() {
    log_info "Validating resource group: $RESOURCE_GROUP_NAME"
    
    if az group show --name "$RESOURCE_GROUP_NAME" &> /dev/null; then
        log_success "Resource group '$RESOURCE_GROUP_NAME' exists"
        
        # Get resource group details
        local rg_location=$(az group show --name "$RESOURCE_GROUP_NAME" --query "location" -o tsv)
        log_info "Resource group location: $rg_location"
    else
        log_error "Resource group '$RESOURCE_GROUP_NAME' does not exist in subscription '$AZURE_SUBSCRIPTION_ID'"
        log_info "Available resource groups:"
        az group list --query "[].{Name:name, Location:location}" -o table
        exit 1
    fi
}

# Function to get server details - just dump JSON
get_detailed_server_info() {
    log_info "Fetching server details..."

    # Get full server details and save to JSON
    az mysql flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$SERVER_NAME" \
        --output json > "${SERVER_NAME}_server_details.json" 2>/dev/null

    if [[ -f "${SERVER_NAME}_server_details.json" && -s "${SERVER_NAME}_server_details.json" ]]; then
        log_success "✓ Server details: ${SERVER_NAME}_server_details.json"
    else
        log_warning "⚠ Failed to fetch server details"
        rm -f "${SERVER_NAME}_server_details.json" 2>/dev/null
        return 1
    fi
}

# Function to discover and collect replica server data
discover_replica_servers() {
    log_info "Discovering replicas..."

    # Get all servers in resource group and save to JSON
    az mysql flexible-server list \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json > "${SERVER_NAME}_all_servers.json" 2>/dev/null

    if [[ -f "${SERVER_NAME}_all_servers.json" && -s "${SERVER_NAME}_all_servers.json" ]]; then
        local server_count=$(jq '. | length' "${SERVER_NAME}_all_servers.json" 2>/dev/null || echo "0")
        log_success "✓ All servers: ${SERVER_NAME}_all_servers.json ($server_count servers)"

        # Find replica servers for the current primary server
        local replicas=$(jq -r --arg primary_server "$SERVER_NAME" '.[] | select(.sourceServerResourceId != null and (.sourceServerResourceId | contains($primary_server))) | .name' "${SERVER_NAME}_all_servers.json" 2>/dev/null)

        local replica_count=0
        local replica_array="["

        if [[ -n "$replicas" ]]; then
            while IFS= read -r replica_name; do
                if [[ -n "$replica_name" && "$replica_name" != "" ]]; then
                    log_info "Found replica: $replica_name"

                    # Collect replica data in same format as primary
                    collect_replica_data "$replica_name"

                    ((replica_count++))
                    if [[ "$replica_array" != "[" ]]; then
                        replica_array+=","
                    fi
                    replica_array+="\"$replica_name\""
                fi
            done <<< "$replicas"
        fi

        replica_array+="]"

        # Create replication info
        cat > "${SERVER_NAME}_replication_info.json" << EOF
{
  "server_name": "$SERVER_NAME",
  "resource_group": "$RESOURCE_GROUP_NAME",
  "server_role": "primary",
  "replica_servers": $replica_array,
  "replica_count": $replica_count,
  "total_servers_in_group": $server_count
}
EOF
        log_success "✓ Replication info: ${SERVER_NAME}_replication_info.json"

        if [[ $replica_count -gt 0 ]]; then
            log_success "✓ Found and collected data for $replica_count replica server(s)"
        else
            log_info "No replica servers found for $SERVER_NAME"
        fi
    else
        log_warning "⚠ Failed to list servers"
        rm -f "${SERVER_NAME}_all_servers.json" 2>/dev/null
        return 1
    fi
}

# Function to collect replica server data in same format as primary
collect_replica_data() {
    local replica_name="$1"

    log_info "Collecting data for replica: $replica_name"

    # Get replica server details
    az mysql flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$replica_name" \
        --output json > "${replica_name}_server_details.json" 2>/dev/null

    if [[ -f "${replica_name}_server_details.json" && -s "${replica_name}_server_details.json" ]]; then
        log_success "✓ Replica details: ${replica_name}_server_details.json"
    else
        log_warning "⚠ Failed to get replica details for $replica_name"
        rm -f "${replica_name}_server_details.json" 2>/dev/null
    fi

    # Get replica parameters if enabled
    if [[ "$FETCH_PARAMETERS" == "true" ]]; then
        az mysql flexible-server parameter list \
            --server-name "$replica_name" \
            --resource-group "$RESOURCE_GROUP_NAME" \
            --output json > "${replica_name}_parameters.json" 2>/dev/null

        if [[ -f "${replica_name}_parameters.json" && -s "${replica_name}_parameters.json" ]]; then
            local param_count=$(jq '. | length' "${replica_name}_parameters.json" 2>/dev/null || echo "0")
            log_success "✓ Replica parameters: ${replica_name}_parameters.json ($param_count parameters)"
        else
            log_warning "⚠ Failed to get replica parameters for $replica_name"
            rm -f "${replica_name}_parameters.json" 2>/dev/null
        fi
    fi

    # Collect replica metrics if enabled
    if [[ "$COLLECT_METRICS" == "true" ]]; then
        collect_replica_metrics "$replica_name"
    fi
}

# Function to collect metrics for replica server
collect_replica_metrics() {
    local replica_name="$1"

    log_info "Collecting metrics for replica: $replica_name"

    # Get replica resource ID
    local replica_resource_id=$(az mysql flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$replica_name" \
        --query "id" -o tsv 2>/dev/null)

    if [[ -z "$replica_resource_id" ]]; then
        log_warning "⚠ Could not get resource ID for replica: $replica_name"
        return 1
    fi

    # Calculate time range (same as primary)
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local start_time

    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
    else
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
    fi

    # Collect same metrics as primary server
    IFS=',' read -ra METRICS_ARRAY <<< "$METRICS_LIST"
    local metrics_collected=0
    local metrics_failed=0

    for metric_name in "${METRICS_ARRAY[@]}"; do
        metric_name=$(echo "$metric_name" | xargs)
        [[ -z "$metric_name" ]] && continue

        # Use same collect_metric function but with replica name prefix
        if collect_metric_for_server "$metric_name" "$replica_resource_id" "$start_time" "$end_time" "$replica_name"; then
            ((metrics_collected++))
        else
            ((metrics_failed++))
        fi
    done

    log_success "Replica $replica_name: Collected $metrics_collected metrics (Failed: $metrics_failed)"
}

# Function to collect metric for any server (primary or replica)
collect_metric_for_server() {
    local metric_name="$1"
    local resource_id="$2"
    local start_time="$3"
    local end_time="$4"
    local server_name="$5"

    # Try to collect the metric
    az monitor metrics list \
        --resource "$resource_id" \
        --metric "$metric_name" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval "PT5M" \
        --aggregation "Average" \
        --output json > "${server_name}_${metric_name}_metrics.json" 2>/dev/null

    # Check if file was created
    if [[ -f "${server_name}_${metric_name}_metrics.json" && -s "${server_name}_${metric_name}_metrics.json" ]]; then
        return 0
    else
        rm -f "${server_name}_${metric_name}_metrics.json" 2>/dev/null
        return 1
    fi
}

# Simple function to collect any metric
collect_metric() {
    local metric_name="$1"
    local resource_id="$2"
    local start_time="$3"
    local end_time="$4"

    log_info "Collecting: $metric_name"

    # Try to collect the metric - if it fails, just continue
    az monitor metrics list \
        --resource "$resource_id" \
        --metric "$metric_name" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval "PT5M" \
        --aggregation "Average" \
        --output json > "${SERVER_NAME}_${metric_name}_metrics.json" 2>/dev/null

    # Check if file was created
    if [[ -f "${SERVER_NAME}_${metric_name}_metrics.json" && -s "${SERVER_NAME}_${metric_name}_metrics.json" ]]; then
        log_success "✓ $metric_name"
        return 0
    else
        log_warning "⚠ $metric_name (not available)"
        rm -f "${SERVER_NAME}_${metric_name}_metrics.json" 2>/dev/null
        return 1
    fi
}

# Function to get monitoring metrics
get_monitoring_metrics() {
    log_info "Starting monitoring metrics collection..."

    local time_period_display=""
    case "$METRICS_TIME_PERIOD" in
        "1h") time_period_display="last 1 hour" ;;
        "6h") time_period_display="last 6 hours" ;;
        "12h") time_period_display="last 12 hours" ;;
        "1d") time_period_display="last 1 day" ;;
        "3d") time_period_display="last 3 days" ;;
        "7d") time_period_display="last 7 days" ;;
        "30d") time_period_display="last 30 days" ;;
        *) time_period_display="last 1 hour" ;;
    esac

    log_info "Fetching monitoring metrics ($time_period_display)..."

    # Get current time and calculate start time based on configured period
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")

    log_info "Time range: $hours_ago hours ago to now"

    # Calculate start time (compatible with both GNU date and BSD date)
    local start_time
    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        # GNU date (Linux)
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
        log_info "Using GNU date format"
    else
        # BSD date (macOS)
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
        log_info "Using BSD date format"
    fi

    log_info "Start time: $start_time"
    log_info "End time: $end_time"

    # Get resource ID for metrics
    log_info "Getting resource ID for metrics..."
    local resource_id=$(az mysql flexible-server show --resource-group "$RESOURCE_GROUP_NAME" --name "$SERVER_NAME" --query "id" -o tsv)

    if [[ -z "$resource_id" ]]; then
        log_error "Failed to get resource ID for metrics"
        return 1
    fi

    log_info "Resource ID: $resource_id"

    # Minimal output for background execution

    # Simple metrics collection - just loop through METRICS_LIST
    if [[ "$COLLECT_METRICS" != "true" ]]; then
        log_info "⏭️  Metrics collection disabled"
        return 0
    fi

    local metrics_collected=0
    local metrics_failed=0

    # Convert comma-separated METRICS_LIST to array and loop
    IFS=',' read -ra METRICS_ARRAY <<< "$METRICS_LIST"

    log_info "Collecting ${#METRICS_ARRAY[@]} metrics..."

    for metric_name in "${METRICS_ARRAY[@]}"; do
        metric_name=$(echo "$metric_name" | xargs)  # Trim whitespace
        [[ -z "$metric_name" ]] && continue  # Skip empty

        if collect_metric "$metric_name" "$resource_id" "$start_time" "$end_time"; then
            ((metrics_collected++))
        else
            ((metrics_failed++))
        fi
    done

    log_success "Collected $metrics_collected metrics (Failed: $metrics_failed)"

    # Minimal output for background execution
}

# Function to fetch server parameters - just dump JSON
fetch_server_parameters() {
    if [[ "$FETCH_PARAMETERS" != "true" ]]; then
        log_info "⏭️  Parameter fetching disabled"
        return 0
    fi

    log_info "Fetching server parameters..."

    # Just get parameters and save to JSON - no processing
    az mysql flexible-server parameter list \
        --server-name "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json > "${SERVER_NAME}_parameters.json" 2>/dev/null

    if [[ -f "${SERVER_NAME}_parameters.json" && -s "${SERVER_NAME}_parameters.json" ]]; then
        local param_count=$(jq '. | length' "${SERVER_NAME}_parameters.json" 2>/dev/null || echo "0")
        log_success "✓ Parameters saved: ${SERVER_NAME}_parameters.json ($param_count parameters)"
    else
        log_warning "⚠ Failed to fetch parameters"
        rm -f "${SERVER_NAME}_parameters.json" 2>/dev/null
        return 1
    fi
}

# Function to organize JSON files into service directory
organize_json_files() {
    local service_dir="mysql-flexible-server-${SERVER_NAME}"
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local timestamped_dir="${service_dir}_${timestamp}"

    log_info "Organizing JSON files into directory: $timestamped_dir"

    # Create service directory
    if mkdir -p "$timestamped_dir"; then
        log_success "Created directory: $timestamped_dir"
    else
        log_error "Failed to create directory: $timestamped_dir"
        return 1
    fi

    # Create primary server subdirectory
    local primary_dir="$timestamped_dir/primary-server"
    if mkdir -p "$primary_dir"; then
        log_success "Created primary server directory: $primary_dir"
    else
        log_error "Failed to create primary server directory: $primary_dir"
        return 1
    fi

    # Move primary server JSON files to the primary-server subdirectory
    local files_moved=0

    # Move all JSON files that were actually created (dynamic discovery)
    log_info "Discovering and moving created JSON files..."

    # Find all JSON files that match the server name pattern (excluding replication info)
    local json_files_found=()
    while IFS= read -r -d '' file; do
        local filename=$(basename "$file")
        # Skip replication info file (handled separately)
        if [[ "$filename" != "${SERVER_NAME}_replication_info.json" ]]; then
            json_files_found+=("$filename")
        fi
    done < <(find . -maxdepth 1 -name "${SERVER_NAME}_*.json" -print0 2>/dev/null)

    log_info "Found ${#json_files_found[@]} JSON files to move"

    # Move discovered files (temporarily disable errexit for file operations)
    set +e  # Disable exit on error temporarily
    for json_file in "${json_files_found[@]}"; do
        if [[ -f "$json_file" ]]; then
            if mv "$json_file" "$primary_dir/" 2>/dev/null; then
                log_success "Moved primary file: $json_file -> $primary_dir/"
                ((files_moved++))
            else
                log_error "Failed to move primary file: $json_file"
                # Continue with other files instead of exiting
            fi
        fi
    done
    set -e  # Re-enable exit on error

    # Move replication info to main directory (not primary subdirectory)
    set +e  # Disable exit on error temporarily
    if [[ -f "${SERVER_NAME}_replication_info.json" ]]; then
        if mv "${SERVER_NAME}_replication_info.json" "$timestamped_dir/" 2>/dev/null; then
            log_success "Moved replication info: ${SERVER_NAME}_replication_info.json -> $timestamped_dir/"
            ((files_moved++))
        else
            log_error "Failed to move replication info file"
            # Continue instead of exiting
        fi
    fi
    set -e  # Re-enable exit on error

    # Organize replica files into numbered subdirectories
    organize_replica_files "$timestamped_dir"

    # Create a summary file with key information
    local summary_file="$timestamped_dir/summary.json"
    create_summary_json "$timestamped_dir" "$summary_file"

    log_success "Organized $files_moved JSON files into: $timestamped_dir"
    log_info "Summary file created: $summary_file"

    # Minimal output for background execution
    log_success "✓ Organized files: $timestamped_dir"

    # Set global variable for directory name (to avoid output capture issues)
    OUTPUT_DIRECTORY="$timestamped_dir"

    return 0  # Ensure function returns success
}

# Function to organize replica files into numbered subdirectories
organize_replica_files() {
    local base_dir="$1"
    local replica_count=0

    # Check if replication info exists to determine if there are replicas
    if [[ -f "$base_dir/${SERVER_NAME}_replication_info.json" ]]; then
        local replica_servers=""
        # Use safer jq command that won't fail if the structure is different
        if command -v jq >/dev/null 2>&1; then
            replica_servers=$(jq -r '.replica_servers[]?' "$base_dir/${SERVER_NAME}_replication_info.json" 2>/dev/null || echo "")
        fi

        if [[ -n "$replica_servers" ]]; then
            log_info "Organizing replica files into subdirectories..."

            while IFS= read -r replica_name; do
                if [[ -n "$replica_name" ]]; then
                    ((replica_count++))
                    local replica_dir="$base_dir/replica-node-$replica_count"

                    # Create replica subdirectory
                    if mkdir -p "$replica_dir"; then
                        log_success "Created replica directory: $replica_dir"

                        # Move all replica-specific files (dynamically find them)
                        local replica_files_moved=0

                        # Find all files that start with replica name (with safe globbing)
                        shopt -s nullglob  # Enable nullglob to handle empty matches
                        local replica_files=(${replica_name}_*.json)
                        shopt -u nullglob  # Disable nullglob

                        if [[ ${#replica_files[@]} -gt 0 ]]; then
                            for replica_file in "${replica_files[@]}"; do
                                if [[ -f "$replica_file" ]]; then
                                    if mv "$replica_file" "$replica_dir/"; then
                                        log_success "Moved replica file: $replica_file -> $replica_dir/"
                                        ((replica_files_moved++))
                                    else
                                        log_error "Failed to move replica file: $replica_file"
                                    fi
                                fi
                            done
                        else
                            log_info "No replica files found for $replica_name (already moved or not created)"
                        fi

                        # Create a replica info file in the replica directory
                        cat > "$replica_dir/replica_info.json" << EOF
{
  "replica_name": "$replica_name",
  "replica_node_number": $replica_count,
  "primary_server": "$SERVER_NAME",
  "files_collected": $replica_files_moved,
  "collection_timestamp": "$(date -u +"%Y-%m-%d %H:%M:%S UTC")"
}
EOF
                        log_success "Created replica info file: $replica_dir/replica_info.json"

                    else
                        log_error "Failed to create replica directory: $replica_dir"
                    fi
                fi
            done <<< "$replica_servers"

            log_success "Organized $replica_count replica node(s) into subdirectories"
        else
            log_info "No replica servers found - creating standalone server structure"
        fi
    else
        log_info "No replication info available - assuming standalone server"
    fi
}

# Function to create a summary JSON file
create_summary_json() {
    local dir="$1"
    local summary_file="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S UTC')

    # Extract key information from the server details JSON
    local server_json="$dir/primary-server/${SERVER_NAME}_server_details.json"

    if [[ ! -f "$server_json" ]]; then
        log_error "Server details JSON not found for summary"
        return 1
    fi

    # No metrics calculations - just raw JSON files

    # Extract vCPU from SKU name for summary
    local sku_name=$(jq -r '.sku.name' "$server_json")
    local server_capacity="N/A"
    if [[ "$sku_name" != "null" && "$sku_name" != "N/A" ]]; then
        # Extract number from SKU name patterns like Standard_B1ms, Standard_D2ds_v4, etc.
        server_capacity=$(echo "$sku_name" | sed -E 's/.*[_]([BDEGM])([0-9]+).*/\2/' | head -1)
        # If extraction failed, try alternative pattern
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity=$(echo "$sku_name" | sed -E 's/.*([0-9]+).*/\1/' | head -1)
        fi
        # If still no valid number, set to N/A
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity="N/A"
        fi
    fi

    # Create summary JSON
    cat > "$summary_file" << EOF
{
  "report_metadata": {
    "timestamp": "$timestamp",
    "subscription_id": "$AZURE_SUBSCRIPTION_ID",
    "resource_group": "$RESOURCE_GROUP_NAME",
    "server_name": "$SERVER_NAME",
    "metrics_time_period": "$METRICS_TIME_PERIOD",
    "service_type": "Azure Database for MySQL - Flexible Server"
  },
  "server_summary": {
    "status": $(jq '.state' "$server_json"),
    "location": $(jq '.location' "$server_json"),
    "availability_zone": $(jq '.availabilityZone' "$server_json"),
    "mysql_version": $(jq '.version' "$server_json"),
    "fqdn": $(jq '.fullyQualifiedDomainName' "$server_json"),
    "pricing_tier": $(jq '.sku.tier' "$server_json"),
    "sku_name": $(jq '.sku.name' "$server_json"),
    "vcpu_capacity": "$server_capacity",
    "storage_size_gb": $(jq '.storage.storageSizeGb' "$server_json"),
    "storage_iops": $(jq '.storage.iops' "$server_json"),
    "backup_retention_days": $(jq '.backup.backupRetentionDays' "$server_json"),
    "high_availability_mode": $(jq '.highAvailability.mode' "$server_json")
  },
  "data_files": {
    "primary_server_directory": "primary-server/",
    "replication_info": "${SERVER_NAME}_replication_info.json",
    "replica_directories": "replica-node-*/",
    "note": "All data files are organized in directories for post-processing"
  }
}
EOF

    log_success "Summary JSON created with key metrics and file references"
}

# Function to check MySQL Flexible Server
check_mysql_flexible_server() {
    log_info "Checking MySQL Flexible Server: $SERVER_NAME"

    # Check if the server exists
    if az mysql flexible-server show --resource-group "$RESOURCE_GROUP_NAME" --name "$SERVER_NAME" &> /dev/null; then
        log_success "MySQL Flexible Server '$SERVER_NAME' found"

        # Get detailed server information
        get_detailed_server_info

        # Get monitoring metrics
        log_info "About to call get_monitoring_metrics function..."
        if get_monitoring_metrics; then
            log_success "Monitoring metrics collection completed"
        else
            log_error "Failed to collect monitoring metrics"
        fi

        # Fetch server parameters if enabled
        fetch_server_parameters

        # Discover and analyze replica servers
        discover_replica_servers

        # Organize JSON files into service directory
        if organize_json_files; then
            log_success "File organization completed successfully"
        else
            log_warning "File organization had issues, but continuing to ZIP creation"
        fi

        # Create ZIP file if requested
        create_zip_file

    else
        log_error "MySQL Flexible Server '$SERVER_NAME' not found in resource group '$RESOURCE_GROUP_NAME'"
        log_info "Available MySQL Flexible Servers in resource group:"
        az mysql flexible-server list --resource-group "$RESOURCE_GROUP_NAME" --query "[].{Name:name, State:state, Location:location}" -o table
        exit 1
    fi
}

# Function to parse command line arguments
parse_arguments() {
    GENERATE_ZIP="false"
    CONFIG_FILE=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --generate-zip)
                GENERATE_ZIP="true"
                shift
                ;;
            --generate-zip=*)
                GENERATE_ZIP="${1#*=}"
                shift
                ;;
            -*)
                log_error "Unknown option: $1"
                log_info "Usage: $0 [--generate-zip] <config_file_path>"
                exit 1
                ;;
            *)
                if [[ -z "$CONFIG_FILE" ]]; then
                    CONFIG_FILE="$1"
                else
                    log_error "Multiple config files specified"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Validate generate_zip value
    if [[ "$GENERATE_ZIP" != "true" && "$GENERATE_ZIP" != "false" ]]; then
        log_error "Invalid --generate-zip value: $GENERATE_ZIP"
        log_info "--generate-zip must be 'true' or 'false'"
        exit 1
    fi
}

# Function to create ZIP file if requested
create_zip_file() {
    if [[ "$GENERATE_ZIP" != "true" ]]; then
        log_info "ZIP creation disabled (GENERATE_ZIP=false)"
        return 0
    fi

    if [[ -z "$OUTPUT_DIRECTORY" || ! -d "$OUTPUT_DIRECTORY" ]]; then
        log_error "Output directory not found for ZIP creation: $OUTPUT_DIRECTORY"
        return 1
    fi

    log_info "Creating ZIP file for directory: $OUTPUT_DIRECTORY"

    local zip_file="${OUTPUT_DIRECTORY}.zip"

    # Remove existing ZIP file if it exists
    if [[ -f "$zip_file" ]]; then
        log_info "Removing existing ZIP file: $zip_file"
        rm -f "$zip_file"
    fi

    if command -v zip &> /dev/null; then
        log_info "Using zip utility to create archive..."

        # Create ZIP archive with verbose output for debugging
        if zip -r "$zip_file" "$OUTPUT_DIRECTORY" 2>&1; then
            if [[ -f "$zip_file" ]]; then
                log_success "ZIP archive created successfully: $zip_file"

                # Get ZIP file size for reporting
                local zip_size
                if command -v du &> /dev/null; then
                    zip_size=$(du -h "$zip_file" 2>/dev/null | cut -f1)
                    log_info "ZIP file size: $zip_size"
                fi

                # Get file count in ZIP
                local file_count
                if command -v unzip &> /dev/null; then
                    file_count=$(unzip -l "$zip_file" 2>/dev/null | tail -1 | awk '{print $2}')
                    log_info "Files in ZIP: $file_count"
                fi

                # Remove original directory
                log_info "Removing original directory: $OUTPUT_DIRECTORY"
                if rm -rf "$OUTPUT_DIRECTORY"; then
                    log_success "Original directory removed successfully"
                    log_info "Final output: $zip_file"
                else
                    log_error "Failed to remove original directory: $OUTPUT_DIRECTORY"
                    log_warning "ZIP file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                log_error "ZIP file was not created successfully"
                return 1
            fi
        else
            log_error "Failed to create ZIP archive using zip utility"
            return 1
        fi
    elif command -v tar &> /dev/null; then
        log_info "ZIP utility not available, using tar with gzip compression..."

        local tar_file="${output_dir}.tar.gz"

        # Remove existing tar file if it exists
        if [[ -f "$tar_file" ]]; then
            log_info "Removing existing tar file: $tar_file"
            rm -f "$tar_file"
        fi

        if tar -czf "$tar_file" "$output_dir" 2>&1; then
            if [[ -f "$tar_file" ]]; then
                log_success "TAR.GZ archive created successfully: $tar_file"

                # Get file size
                local tar_size
                if command -v du &> /dev/null; then
                    tar_size=$(du -h "$tar_file" 2>/dev/null | cut -f1)
                    log_info "TAR.GZ file size: $tar_size"
                fi

                # Remove original directory
                log_info "Removing original directory: $output_dir"
                if rm -rf "$output_dir"; then
                    log_success "Original directory removed successfully"
                    log_info "Final output: $tar_file"
                else
                    log_error "Failed to remove original directory: $output_dir"
                    log_warning "TAR.GZ file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                log_error "TAR.GZ file was not created successfully"
                return 1
            fi
        else
            log_error "Failed to create TAR.GZ archive"
            return 1
        fi
    else
        log_warning "Neither zip nor tar utilities are available, skipping archive creation"
        log_info "Please install zip or tar to enable archive creation"
        log_info "Data is available in directory: $output_dir"
        return 1
    fi
}

# Main function
main() {
    local config_file="$1"

    log_info "Starting Azure MySQL Flexible Server validation script"
    log_info "=================================================="

    # Check if config file is provided
    if [[ -z "$config_file" ]]; then
        log_error "No configuration file provided"
        exit 1
    fi

    log_info "Generate ZIP: $GENERATE_ZIP"
    
    # Step 1: Check prerequisites
    check_prerequisites

    # Step 2: Validate and load configuration
    validate_config "$config_file"
    
    # Step 3: Check Azure login and subscription
    check_azure_login
    
    # Step 4: Validate resource group
    validate_resource_group
    
    # Step 5: Check MySQL Flexible Server
    check_mysql_flexible_server
    
    log_success "All validations completed successfully!"
    log_info "=================================================="
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    CONFIG_FILE=""
    GENERATE_ZIP_ARG=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --config-path)
                if [[ -n "$2" && "$2" != --* ]]; then
                    CONFIG_FILE="$2"
                    shift 2
                else
                    echo "Error: --config-path requires a file path argument"
                    exit 1
                fi
                ;;
            --generate-zip)
                if [[ -n "$2" && "$2" != --* ]]; then
                    case "$2" in
                        true|false)
                            GENERATE_ZIP_ARG="$2"
                            shift 2
                            ;;
                        *)
                            echo "Error: --generate-zip requires 'true' or 'false' as argument"
                            exit 1
                            ;;
                    esac
                else
                    echo "Error: --generate-zip requires 'true' or 'false' as argument"
                    exit 1
                fi
                ;;
            --help|-h)
                echo "Azure MySQL Flexible Server Monitoring Script"
                echo ""
                echo "Usage: $0 --config-path <config-file> [--generate-zip <true|false>]"
                echo ""
                echo "Required Arguments:"
                echo "  --config-path PATH    Path to configuration file (.json or .conf)"
                echo ""
                echo "Optional Arguments:"
                echo "  --generate-zip BOOL   Create ZIP archive (default: true)"
                echo "  --help, -h           Show this help message"
                echo ""
                echo "Examples:"
                echo "  # JSON configuration (recommended) - ZIP generated by default"
                echo "  $0 --config-path config.json"
                echo ""
                echo "  # Legacy .conf configuration - ZIP generated by default"
                echo "  $0 --config-path config.conf"
                echo ""
                echo "  # Disable ZIP generation (keep directory structure)"
                echo "  $0 --config-path config.json --generate-zip false"
                echo ""
                echo "JSON Configuration Format:"
                echo "  {"
                echo "    \"azure\": {"
                echo "      \"subscription_id\": \"your-subscription-id\","
                echo "      \"resource_group\": \"your-resource-group\","
                echo "      \"server_name\": \"your-mysql-server\""
                echo "    },"
                echo "    \"monitoring\": {"
                echo "      \"metrics_time_period\": \"1h\""
                echo "    },"
                echo "    \"collection\": {"
                echo "      \"fetch_parameters\": true,"
                echo "      \"collect_metrics\": true,"
                echo "      \"metrics_list\": [\"cpu_percent\", \"memory_percent\", \"active_connections\"]"
                echo "    }"
                echo "  }"
                echo ""
                echo "Legacy .conf Configuration Format:"
                echo "  AZURE_SUBSCRIPTION_ID=\"your-subscription-id\""
                echo "  RESOURCE_GROUP_NAME=\"your-resource-group\""
                echo "  SERVER_NAME=\"your-mysql-server\""
                echo "  METRICS_TIME_PERIOD=\"1h\"  # Optional: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
                echo "  FETCH_PARAMETERS=\"true\"   # Optional: true/false"
                echo "  COLLECT_METRICS=\"true\"    # Optional: true/false"
                exit 0
                ;;
            *)
                echo "Error: Unknown argument '$1'"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$CONFIG_FILE" ]]; then
        echo "Error: Missing required argument --config-path"
        echo "Usage: $0 --config-path <config-file> [--generate-zip <true|false>]"
        echo "Use --help for more information"
        exit 1
    fi

    # Validate config file exists
    if [[ ! -f "$CONFIG_FILE" ]]; then
        echo "Error: Configuration file '$CONFIG_FILE' not found"
        exit 1
    fi

    # Set ZIP generation flag (default to true if not specified)
    export GENERATE_ZIP="${GENERATE_ZIP_ARG:-true}"

    # Execute main function with provided config file
    main "$CONFIG_FILE"
fi
