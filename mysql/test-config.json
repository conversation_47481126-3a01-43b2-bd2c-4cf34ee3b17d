{"azure": {"subscription_id": "207a84e2-208b-4809-baab-e94a043914f5", "resource_group": "amit-test", "server_name": "amit-mysqldb2"}, "monitoring": {"metrics_time_period": "1h", "_supported_values": "1h, 6h, 12h, 1d, 3d, 7d, 30d"}, "collection": {"fetch_parameters": true, "collect_metrics": true, "metrics_list": ["cpu_percent", "memory_percent", "active_connections", "io_consumption_percent", "network_bytes_ingress", "replication_lag", "backup_storage_used", "serverlog_storage_usage", "aborted_connections", "total_connections", "Slow_queries", "Queries", "Com_select", "Com_insert", "Threads_running", "storage_limit", "storage_used", "data_storage_used", "binlog_storage_used", "serverlog_storage_percent", "Innodb_buffer_pool_reads", "Innodb_buffer_pool_read_requests", "Innodb_data_writes", "Uptime", "active_transactions"]}}