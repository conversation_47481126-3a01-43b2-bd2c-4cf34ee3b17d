#!/bin/bash

# Azure MySQL Flexible Server Validation Script
# Usage: ./azure_mysql_flexible_server_check.sh <config_file_path>

# Note: Removed -e to allow graceful error handling and continuation

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get timestamp with both local and UTC time
get_timestamp() {
    local local_time=$(date +"%Y-%m-%d %H:%M:%S")
    local utc_time=$(date -u +"%Y-%m-%d %H:%M:%S")
    echo "${local_time} (UTC: ${utc_time})"
}

# Function to print colored output with timestamps
log_info() {
    echo -e "${BLUE}[$(get_timestamp)] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(get_timestamp)] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(get_timestamp)] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(get_timestamp)] [ERROR]${NC} $1"
}

# Function to execute Azure CLI commands safely with error handling
safe_az_command() {
    local command_description="$1"
    shift
    local az_command=("$@")

    log_info "Executing: $command_description" >&2

    local output
    local exit_code

    # Execute the command and capture both output and exit code
    if output=$("${az_command[@]}" 2>&1); then
        exit_code=0
        echo "$output"
        return 0
    else
        exit_code=$?
        print_error "Failed: $command_description" >&2
        print_error "Command: ${az_command[*]}" >&2
        print_error "Error output: $output" >&2
        print_warning "Continuing with next operation..." >&2
        # Return empty output to avoid jq parse errors
        echo ""
        return $exit_code
    fi
}

# Function to check if required tools are installed
check_prerequisites() {
    local missing_tools=()

    # Check Azure CLI
    if ! command -v az &> /dev/null; then
        missing_tools+=("Azure CLI")
        print_error "Azure CLI is not installed."
        print_info "Install: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    fi

    # Check jq for JSON parsing
    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
        print_error "jq is not installed (required for JSON parsing)."
        print_info "Install jq:"
        print_info "  macOS: brew install jq"
        print_info "  Ubuntu/Debian: sudo apt-get install jq"
        print_info "  CentOS/RHEL: sudo yum install jq"
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi

    print_success "All required tools are available"
}

# Function to validate time period format
validate_time_period() {
    local time_period="$1"

    # Valid time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d
    case "$time_period" in
        "1h"|"6h"|"12h"|"1d"|"3d"|"7d"|"30d")
            print_success "Valid time period: $time_period"
            ;;
        *)
            print_error "Invalid time period: $time_period"
            print_info "Supported time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
            print_info "Examples: '1h' = 1 hour, '1d' = 1 day, '7d' = 7 days"
            exit 1
            ;;
    esac
}

# Function to convert time period to hours for date calculation
convert_time_period_to_hours() {
    local time_period="$1"

    case "$time_period" in
        "1h") echo "1" ;;
        "6h") echo "6" ;;
        "12h") echo "12" ;;
        "1d") echo "24" ;;
        "3d") echo "72" ;;
        "7d") echo "168" ;;
        "30d") echo "720" ;;
        *) echo "1" ;;  # Default to 1 hour
    esac
}

# Function to validate time period format
validate_time_period() {
    local time_period="$1"

    # Valid time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d
    case "$time_period" in
        "1h"|"6h"|"12h"|"1d"|"3d"|"7d"|"30d")
            print_success "Valid time period: $time_period"
            ;;
        *)
            print_error "Invalid time period: $time_period"
            print_info "Supported time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
            print_info "Examples: '1h' = 1 hour, '1d' = 1 day, '7d' = 7 days"
            exit 1
            ;;
    esac
}

# Function to convert time period to hours for date calculation
convert_time_period_to_hours() {
    local time_period="$1"

    case "$time_period" in
        "1h") echo "1" ;;
        "6h") echo "6" ;;
        "12h") echo "12" ;;
        "1d") echo "24" ;;
        "3d") echo "72" ;;
        "7d") echo "168" ;;
        "30d") echo "720" ;;
        *) echo "1" ;;  # Default to 1 hour
    esac
}

# Function to check metric thresholds and generate alerts
check_metric_threshold() {
    local metric_name="$1"
    local metric_value="$2"
    local threshold="$3"
    local unit="$4"

    if [[ -n "$threshold" && "$metric_value" != "N/A" ]]; then
        # Convert values to integers for comparison (remove decimal places)
        local metric_int=$(echo "$metric_value" | cut -d'.' -f1)
        local threshold_int=$(echo "$threshold" | cut -d'.' -f1)

        if [[ "$metric_int" -gt "$threshold_int" ]]; then
            print_warning "🚨 ALERT: $metric_name ($metric_value$unit) exceeds threshold ($threshold$unit)"
            return 1
        else
            print_success "✅ $metric_name ($metric_value$unit) is within threshold ($threshold$unit)"
            return 0
        fi
    fi
    return 0
}

# Function to validate config file and extract values
validate_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        print_error "Config file '$config_file' not found"
        exit 1
    fi
    
    print_info "Reading configuration from: $config_file"
    
    # Source the config file
    source "$config_file"
    
    # Validate required variables
    if [[ -z "$AZURE_SUBSCRIPTION_ID" ]]; then
        print_error "AZURE_SUBSCRIPTION_ID not found in config file"
        exit 1
    fi

    if [[ -z "$RESOURCE_GROUP_NAME" ]]; then
        print_error "RESOURCE_GROUP_NAME not found in config file"
        exit 1
    fi

    if [[ -z "$SERVER_NAME" ]]; then
        print_error "SERVER_NAME not found in config file"
        exit 1
    fi

    # Set default values for optional parameters
    METRICS_TIME_PERIOD="${METRICS_TIME_PERIOD:-1h}"
    FETCH_PARAMETERS="${FETCH_PARAMETERS:-false}"

    # Validate time period format
    validate_time_period "$METRICS_TIME_PERIOD"

    # Validate fetch parameters setting
    if [[ "$FETCH_PARAMETERS" != "true" && "$FETCH_PARAMETERS" != "false" ]]; then
        print_error "Invalid FETCH_PARAMETERS value: $FETCH_PARAMETERS"
        print_info "FETCH_PARAMETERS must be 'true' or 'false'"
        exit 1
    fi

    print_success "Configuration validated successfully"
    print_info "Subscription ID: $AZURE_SUBSCRIPTION_ID"
    print_info "Resource Group: $RESOURCE_GROUP_NAME"
    print_info "Server Name: $SERVER_NAME"
    print_info "Metrics Time Period: $METRICS_TIME_PERIOD"
    print_info "Fetch Parameters: $FETCH_PARAMETERS"
}

# Function to check current Azure login status
check_azure_login() {
    print_info "Checking Azure login status..."
    
    # Check if user is logged in
    if ! az account show &> /dev/null; then
        print_error "Not logged into Azure. Please run 'az login' first."
        exit 1
    fi
    
    # Get current subscription
    local current_subscription=$(az account show --query "id" -o tsv 2>/dev/null)
    
    if [[ -z "$current_subscription" ]]; then
        print_error "Unable to retrieve current subscription information"
        exit 1
    fi
    
    print_success "Currently logged into Azure"
    print_info "Current subscription: $current_subscription"
    
    # Check if the required subscription is available
    if ! az account show --subscription "$AZURE_SUBSCRIPTION_ID" &> /dev/null; then
        print_error "Subscription '$AZURE_SUBSCRIPTION_ID' is not available or accessible"
        print_info "Available subscriptions:"
        az account list --query "[].{Name:name, SubscriptionId:id, State:state}" -o table
        exit 1
    fi
    
    # Set the subscription if it's different from current
    if [[ "$current_subscription" != "$AZURE_SUBSCRIPTION_ID" ]]; then
        print_info "Switching to subscription: $AZURE_SUBSCRIPTION_ID"
        if az account set --subscription "$AZURE_SUBSCRIPTION_ID"; then
            print_success "Successfully switched to subscription: $AZURE_SUBSCRIPTION_ID"
        else
            print_error "Failed to switch to subscription: $AZURE_SUBSCRIPTION_ID"
            exit 1
        fi
    else
        print_success "Already using the correct subscription"
    fi
}

# Function to validate resource group
validate_resource_group() {
    print_info "Validating resource group: $RESOURCE_GROUP_NAME"
    
    if az group show --name "$RESOURCE_GROUP_NAME" &> /dev/null; then
        print_success "Resource group '$RESOURCE_GROUP_NAME' exists"
        
        # Get resource group details
        local rg_location=$(az group show --name "$RESOURCE_GROUP_NAME" --query "location" -o tsv)
        print_info "Resource group location: $rg_location"
    else
        print_error "Resource group '$RESOURCE_GROUP_NAME' does not exist in subscription '$AZURE_SUBSCRIPTION_ID'"
        print_info "Available resource groups:"
        az group list --query "[].{Name:name, Location:location}" -o table
        exit 1
    fi
}

# Function to get detailed server information
get_detailed_server_info() {
    print_info "Fetching comprehensive server information..."

    # Get full server details
    local server_json=$(az mysql flexible-server show --resource-group "$RESOURCE_GROUP_NAME" --name "$SERVER_NAME" --output json)

    # Save full details to file
    echo "$server_json" > "${SERVER_NAME}_full_details.json"
    print_success "Full server details saved to: ${SERVER_NAME}_full_details.json"

    # Extract detailed information
    local server_state=$(echo "$server_json" | jq -r '.state // "N/A"')
    local server_version=$(echo "$server_json" | jq -r '.version // "N/A"')
    local server_location=$(echo "$server_json" | jq -r '.location // "N/A"')
    local server_tier=$(echo "$server_json" | jq -r '.sku.tier // "N/A"')
    local server_sku=$(echo "$server_json" | jq -r '.sku.name // "N/A"')
    # Extract vCPU from SKU name (e.g., Standard_B1ms -> 1, Standard_D2ds_v4 -> 2)
    local sku_name=$(echo "$server_json" | jq -r '.sku.name // "N/A"')
    local server_capacity="N/A"
    if [[ "$sku_name" != "N/A" ]]; then
        # Extract number from SKU name patterns like Standard_B1ms, Standard_D2ds_v4, etc.
        server_capacity=$(echo "$sku_name" | sed -E 's/.*[_]([BDEGM])([0-9]+).*/\2/' | head -1)
        # If extraction failed, try alternative pattern
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity=$(echo "$sku_name" | sed -E 's/.*([0-9]+).*/\1/' | head -1)
        fi
        # If still no valid number, set to N/A
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity="N/A"
        fi
    fi
    local storage_size=$(echo "$server_json" | jq -r '.storage.storageSizeGb // "N/A"')
    local storage_tier=$(echo "$server_json" | jq -r '.storage.tier // "N/A"')
    local storage_iops=$(echo "$server_json" | jq -r '.storage.iops // "N/A"')
    local backup_retention=$(echo "$server_json" | jq -r '.backup.backupRetentionDays // "N/A"')
    local ha_enabled=$(echo "$server_json" | jq -r '.highAvailability.mode // "Disabled"')
    local ha_zone=$(echo "$server_json" | jq -r '.highAvailability.standbyAvailabilityZone // "N/A"')
    local availability_zone=$(echo "$server_json" | jq -r '.availabilityZone // "N/A"')
    local fqdn=$(echo "$server_json" | jq -r '.fullyQualifiedDomainName // "N/A"')

    # Display comprehensive information
    echo ""
    echo "=================================================="
    echo "           MYSQL FLEXIBLE SERVER DETAILS"
    echo "=================================================="
    printf "%-25s: %s\n" "Server Name" "$SERVER_NAME"
    printf "%-25s: %s\n" "Status" "$server_state"
    printf "%-25s: %s\n" "Region/Location" "$server_location"
    printf "%-25s: %s\n" "Availability Zone" "$availability_zone"
    printf "%-25s: %s\n" "MySQL Version" "$server_version"
    printf "%-25s: %s\n" "FQDN" "$fqdn"
    echo ""
    echo "COMPUTE & INSTANCE DETAILS:"
    printf "%-25s: %s\n" "Pricing Tier" "$server_tier"
    printf "%-25s: %s\n" "SKU/Instance Type" "$server_sku"
    printf "%-25s: %s vCores\n" "vCPU Capacity" "$server_capacity"
    echo ""
    echo "STORAGE INFORMATION:"
    printf "%-25s: %s GB\n" "Storage Size" "$storage_size"
    printf "%-25s: %s\n" "Storage Tier" "$storage_tier"
    printf "%-25s: %s\n" "Storage IOPS" "$storage_iops"
    printf "%-25s: %s days\n" "Backup Retention" "$backup_retention"
    echo ""
    echo "HIGH AVAILABILITY:"
    printf "%-25s: %s\n" "HA Mode" "$ha_enabled"
    if [[ "$ha_enabled" != "Disabled" && "$ha_zone" != "N/A" ]]; then
        printf "%-25s: %s\n" "Standby Zone" "$ha_zone"
        echo "Number of Instances: 2 (Primary + Standby)"
    else
        echo "Number of Instances: 1 (Single instance)"
    fi
    echo "=================================================="
}

# Function to discover and analyze replica servers
discover_replica_servers() {
    print_info "Discovering replica servers for: $SERVER_NAME"

    # Get list of all MySQL flexible servers in the resource group
    local all_servers=$(az mysql flexible-server list --resource-group "$RESOURCE_GROUP_NAME" --output json 2>/dev/null)

    if [[ -z "$all_servers" || "$all_servers" == "null" ]]; then
        print_error "Failed to list servers in resource group"
        return 1
    fi

    # Find replicas of the current server
    local replicas=$(echo "$all_servers" | jq -r --arg source_server "$SERVER_NAME" '.[] | select(.sourceServerResourceId != null and (.sourceServerResourceId | contains($source_server))) | .name')

    # Also check if current server is itself a replica
    local source_server=$(echo "$all_servers" | jq -r --arg current_server "$SERVER_NAME" '.[] | select(.name == $current_server) | .sourceServerResourceId // "null"')

    echo ""
    echo "=================================================="
    echo "           REPLICATION TOPOLOGY"
    echo "=================================================="

    if [[ "$source_server" != "null" && "$source_server" != "" ]]; then
        local source_name=$(echo "$source_server" | sed 's/.*\///')
        printf "%-25s: %s (This server is a READ REPLICA)\n" "Source Server" "$source_name"
        printf "%-25s: %s\n" "Current Server" "$SERVER_NAME"

        # Save replication info
        cat > "${SERVER_NAME}_replication_info.json" << EOF
{
  "server_role": "replica",
  "server_name": "$SERVER_NAME",
  "source_server": "$source_name",
  "source_server_resource_id": "$source_server",
  "replica_servers": []
}
EOF
    else
        printf "%-25s: %s (PRIMARY/SOURCE)\n" "Current Server" "$SERVER_NAME"

        # Check if replicas exist and are not empty
        local has_replicas=false
        local replica_count=0
        local replica_array="["

        if [[ -n "$replicas" ]]; then
            # Count non-empty replica names
            while IFS= read -r replica_name; do
                if [[ -n "$replica_name" && "$replica_name" != "" ]]; then
                    has_replicas=true
                    break
                fi
            done <<< "$replicas"
        fi

        if [[ "$has_replicas" == "true" ]]; then
            echo "Read Replicas:"

            while IFS= read -r replica_name; do
                if [[ -n "$replica_name" && "$replica_name" != "" ]]; then
                    ((replica_count++))
                    printf "  %d. %s\n" "$replica_count" "$replica_name"

                    # Add to JSON array
                    if [[ "$replica_array" != "[" ]]; then
                        replica_array+=","
                    fi
                    replica_array+="\"$replica_name\""

                    # Get replica details
                    get_replica_details "$replica_name"
                fi
            done <<< "$replicas"

            replica_array+="]"

            # Save replication info for primary with replicas
            cat > "${SERVER_NAME}_replication_info.json" << EOF
{
  "server_role": "primary",
  "server_name": "$SERVER_NAME",
  "source_server": null,
  "source_server_resource_id": null,
  "replica_servers": $replica_array,
  "replica_count": $replica_count
}
EOF

            printf "%-25s: %d\n" "Total Replicas" "$replica_count"
            print_success "Found $replica_count read replica(s)"
        else
            printf "%-25s: %s\n" "Read Replicas" "None found"

            # Save replication info for standalone server
            cat > "${SERVER_NAME}_replication_info.json" << EOF
{
  "server_role": "standalone",
  "server_name": "$SERVER_NAME",
  "source_server": null,
  "source_server_resource_id": null,
  "replica_servers": [],
  "replica_count": 0
}
EOF
            print_info "This is a standalone MySQL server (no replicas configured)"
        fi
    fi

    echo "=================================================="
    print_success "Replication topology saved to: ${SERVER_NAME}_replication_info.json"
}

# Function to get detailed information about a replica server
get_replica_details() {
    local replica_name="$1"

    print_info "Collecting details for replica: $replica_name"

    # Get replica server details
    local replica_details=$(az mysql flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$replica_name" \
        --output json 2>/dev/null)

    if [[ -n "$replica_details" && "$replica_details" != "null" ]]; then
        echo "$replica_details" > "${replica_name}_replica_details.json"

        # Extract key information
        local replica_state=$(echo "$replica_details" | jq -r '.state // "N/A"')
        local replica_location=$(echo "$replica_details" | jq -r '.location // "N/A"')
        local replica_sku=$(echo "$replica_details" | jq -r '.sku.name // "N/A"')

        printf "    Status: %s, Location: %s, SKU: %s\n" "$replica_state" "$replica_location" "$replica_sku"

        # Collect replica metrics if enabled
        if [[ "$METRICS_TIME_PERIOD" != "" ]]; then
            collect_replica_metrics "$replica_name"
        fi
    else
        print_warning "Failed to get details for replica: $replica_name"
    fi
}

# Function to collect metrics from replica servers
collect_replica_metrics() {
    local replica_name="$1"

    print_info "Collecting metrics for replica: $replica_name"

    # Get resource ID for the replica
    local replica_resource_id=$(az mysql flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$replica_name" \
        --query "id" -o tsv 2>/dev/null)

    if [[ -z "$replica_resource_id" ]]; then
        print_warning "Could not get resource ID for replica: $replica_name"
        return 1
    fi

    # Calculate time range
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")

    local start_time
    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
    else
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
    fi

    # Collect key metrics for replica
    local metrics_summary="{"

    # CPU metrics
    local cpu_metrics=$(az monitor metrics list \
        --resource "$replica_resource_id" \
        --metric "cpu_percent" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    local avg_cpu="N/A"
    if [[ -n "$cpu_metrics" && "$cpu_metrics" != "null" ]]; then
        avg_cpu=$(echo "$cpu_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        echo "$cpu_metrics" > "${replica_name}_cpu_metrics.json"
    fi

    # Replication lag metrics (most important for replicas)
    local replication_lag_metrics=$(az monitor metrics list \
        --resource "$replica_resource_id" \
        --metric "replication_lag" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    local avg_replication_lag="N/A"
    if [[ -n "$replication_lag_metrics" && "$replication_lag_metrics" != "null" ]]; then
        avg_replication_lag=$(echo "$replication_lag_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        echo "$replication_lag_metrics" > "${replica_name}_replication_lag_metrics.json"
    fi

    # Create replica metrics summary
    cat > "${replica_name}_metrics_summary.json" << EOF
{
  "replica_name": "$replica_name",
  "metrics_time_period": "$METRICS_TIME_PERIOD",
  "avg_cpu_usage_percent": "$avg_cpu",
  "avg_replication_lag_seconds": "$avg_replication_lag",
  "collection_timestamp": "$(date -u +"%Y-%m-%d %H:%M:%S UTC")"
}
EOF

    printf "    Metrics - CPU: %s%%, Replication Lag: %s seconds\n" "$avg_cpu" "$avg_replication_lag"
}

# Function to get monitoring metrics
get_monitoring_metrics() {
    print_info "Starting monitoring metrics collection..."

    local time_period_display=""
    case "$METRICS_TIME_PERIOD" in
        "1h") time_period_display="last 1 hour" ;;
        "6h") time_period_display="last 6 hours" ;;
        "12h") time_period_display="last 12 hours" ;;
        "1d") time_period_display="last 1 day" ;;
        "3d") time_period_display="last 3 days" ;;
        "7d") time_period_display="last 7 days" ;;
        "30d") time_period_display="last 30 days" ;;
        *) time_period_display="last 1 hour" ;;
    esac

    print_info "Fetching monitoring metrics ($time_period_display)..."

    # Get current time and calculate start time based on configured period
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")

    print_info "Time range: $hours_ago hours ago to now"

    # Calculate start time (compatible with both GNU date and BSD date)
    local start_time
    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        # GNU date (Linux)
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
        print_info "Using GNU date format"
    else
        # BSD date (macOS)
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
        print_info "Using BSD date format"
    fi

    print_info "Start time: $start_time"
    print_info "End time: $end_time"

    # Get resource ID for metrics
    print_info "Getting resource ID for metrics..."
    local resource_id=$(az mysql flexible-server show --resource-group "$RESOURCE_GROUP_NAME" --name "$SERVER_NAME" --query "id" -o tsv)

    if [[ -z "$resource_id" ]]; then
        print_error "Failed to get resource ID for metrics"
        return 1
    fi

    print_info "Resource ID: $resource_id"

    echo ""
    echo "=================================================="
    echo "           MONITORING METRICS ($(echo "$time_period_display" | tr '[:lower:]' '[:upper:]'))"
    echo "=================================================="

    # CPU Utilization
    print_info "Fetching CPU utilization..."
    local cpu_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "cpu_percent" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$cpu_metrics" && "$cpu_metrics" != "null" ]]; then
        echo "$cpu_metrics" > "${SERVER_NAME}_cpu_metrics.json"
        local avg_cpu=$(echo "$cpu_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        printf "%-25s: %s%%\n" "Average CPU Usage" "$avg_cpu"
    else
        printf "%-25s: %s\n" "Average CPU Usage" "No data available"
    fi

    # Memory Utilization
    print_info "Fetching memory utilization..."
    local memory_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "memory_percent" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$memory_metrics" && "$memory_metrics" != "null" ]]; then
        echo "$memory_metrics" > "${SERVER_NAME}_memory_metrics.json"
        local avg_memory=$(echo "$memory_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        printf "%-25s: %s%%\n" "Average Memory Usage" "$avg_memory"
    else
        printf "%-25s: %s\n" "Average Memory Usage" "No data available"
    fi

    # Storage Utilization
    print_info "Fetching storage utilization..."
    local storage_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "storage_percent" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$storage_metrics" && "$storage_metrics" != "null" ]]; then
        echo "$storage_metrics" > "${SERVER_NAME}_storage_metrics.json"
        local avg_storage=$(echo "$storage_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        printf "%-25s: %s%%\n" "Average Storage Usage" "$avg_storage"
    else
        printf "%-25s: %s\n" "Average Storage Usage" "No data available"
    fi

    # Active Connections
    print_info "Fetching active connections..."
    local connection_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "active_connections" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$connection_metrics" && "$connection_metrics" != "null" ]]; then
        echo "$connection_metrics" > "${SERVER_NAME}_connection_metrics.json"
        local avg_connections=$(echo "$connection_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
        printf "%-25s: %s\n" "Average Active Connections" "$avg_connections"
    else
        printf "%-25s: %s\n" "Average Active Connections" "No data available"
    fi

    # IO Operations
    print_info "Fetching IO metrics..."
    local io_read_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "io_consumption_percent" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$io_read_metrics" && "$io_read_metrics" != "null" ]]; then
        echo "$io_read_metrics" > "${SERVER_NAME}_io_metrics.json"
        local avg_io=$(echo "$io_read_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        printf "%-25s: %s%%\n" "Average IO Consumption" "$avg_io"
    else
        printf "%-25s: %s\n" "Average IO Consumption" "No data available"
    fi

    # Network metrics
    print_info "Fetching network metrics..."
    local network_in_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "network_bytes_ingress" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Total \
        --output json 2>/dev/null)

    if [[ -n "$network_in_metrics" && "$network_in_metrics" != "null" ]]; then
        echo "$network_in_metrics" > "${SERVER_NAME}_network_in_metrics.json"
        local total_network_in=$(echo "$network_in_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum; else print "N/A"}')
        printf "%-25s: %s bytes\n" "Total Network In" "$total_network_in"
    else
        printf "%-25s: %s\n" "Total Network In" "No data available"
    fi

    local network_out_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "network_bytes_egress" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Total \
        --output json 2>/dev/null)

    if [[ -n "$network_out_metrics" && "$network_out_metrics" != "null" ]]; then
        echo "$network_out_metrics" > "${SERVER_NAME}_network_out_metrics.json"
        local total_network_out=$(echo "$network_out_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum; else print "N/A"}')
        printf "%-25s: %s bytes\n" "Total Network Out" "$total_network_out"
    else
        printf "%-25s: %s\n" "Total Network Out" "No data available"
    fi

    # Replication lag (if applicable)
    print_info "Fetching replication metrics..."
    local replication_lag_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "replication_lag" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$replication_lag_metrics" && "$replication_lag_metrics" != "null" ]]; then
        echo "$replication_lag_metrics" > "${SERVER_NAME}_replication_lag_metrics.json"
        local avg_replication_lag=$(echo "$replication_lag_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        printf "%-25s: %s seconds\n" "Average Replication Lag" "$avg_replication_lag"
    else
        printf "%-25s: %s\n" "Average Replication Lag" "No data available"
    fi

    # Backup storage used
    print_info "Fetching backup storage metrics..."
    local backup_storage_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "backup_storage_used" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$backup_storage_metrics" && "$backup_storage_metrics" != "null" ]]; then
        echo "$backup_storage_metrics" > "${SERVER_NAME}_backup_storage_metrics.json"
        local avg_backup_storage=$(echo "$backup_storage_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
        printf "%-25s: %s bytes\n" "Average Backup Storage" "$avg_backup_storage"
    else
        printf "%-25s: %s\n" "Average Backup Storage" "No data available"
    fi

    # Server log storage used
    print_info "Fetching server log storage metrics..."
    local serverlog_storage_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "serverlog_storage_used" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$serverlog_storage_metrics" && "$serverlog_storage_metrics" != "null" ]]; then
        echo "$serverlog_storage_metrics" > "${SERVER_NAME}_serverlog_storage_metrics.json"
        local avg_serverlog_storage=$(echo "$serverlog_storage_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
        printf "%-25s: %s bytes\n" "Average Server Log Storage" "$avg_serverlog_storage"
    else
        printf "%-25s: %s\n" "Average Server Log Storage" "No data available"
    fi

    # Aborted connections
    print_info "Fetching aborted connections metrics..."
    local aborted_connections_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "aborted_connections" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Total \
        --output json 2>/dev/null)

    if [[ -n "$aborted_connections_metrics" && "$aborted_connections_metrics" != "null" ]]; then
        echo "$aborted_connections_metrics" > "${SERVER_NAME}_aborted_connections_metrics.json"
        local total_aborted_connections=$(echo "$aborted_connections_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum; else print "N/A"}')
        printf "%-25s: %s\n" "Total Aborted Connections" "$total_aborted_connections"
    else
        printf "%-25s: %s\n" "Total Aborted Connections" "No data available"
    fi

    # Queries per second
    print_info "Fetching queries per second metrics..."
    local queries_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "Queries" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Total \
        --output json 2>/dev/null)

    if [[ -n "$queries_metrics" && "$queries_metrics" != "null" ]]; then
        echo "$queries_metrics" > "${SERVER_NAME}_queries_metrics.json"
        local total_queries=$(echo "$queries_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum; else print "N/A"}')
        printf "%-25s: %s\n" "Total Queries" "$total_queries"
    else
        printf "%-25s: %s\n" "Total Queries" "No data available"
    fi

    echo ""
    echo "ADVANCED MYSQL METRICS:"
    echo "=================================================="

    # Additional MySQL-specific metrics
    local advanced_metrics=(
        "slow_queries:Slow Queries"
        "Com_select:SELECT Statements"
        "Com_insert:INSERT Statements"
        "Com_update:UPDATE Statements"
        "Com_delete:DELETE Statements"
        "threads_connected:Connected Threads"
        "threads_running:Running Threads"
        "max_used_connections:Peak Connections"
        "Innodb_buffer_pool_read_requests:Buffer Pool Read Requests"
        "Innodb_buffer_pool_reads:Physical Buffer Reads"
        "Innodb_data_reads:InnoDB Data Reads"
        "Innodb_data_writes:InnoDB Data Writes"
        "Innodb_log_writes:InnoDB Log Writes"
        "aborted_connects:Aborted Connects"
        "connection_errors_internal:Internal Connection Errors"
        "storage_limit:Storage Limit"
    )

    for metric_info in "${advanced_metrics[@]}"; do
        local metric_name="${metric_info%%:*}"
        local metric_display="${metric_info##*:}"

        print_info "Fetching $metric_display..."
        local metric_data=$(az monitor metrics list \
            --resource "$resource_id" \
            --metric "$metric_name" \
            --start-time "$start_time" \
            --end-time "$end_time" \
            --interval PT5M \
            --aggregation Average \
            --output json 2>/dev/null)

        if [[ -n "$metric_data" && "$metric_data" != "null" ]]; then
            echo "$metric_data" > "${SERVER_NAME}_${metric_name}_metrics.json"

            # Calculate appropriate aggregation based on metric type
            local metric_value
            if [[ "$metric_name" =~ ^(Com_|Innodb_|slow_queries|aborted_connects|connection_errors_internal)$ ]]; then
                # Use total for counter metrics
                metric_value=$(echo "$metric_data" | jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum; else print "N/A"}')
            else
                # Use average for gauge metrics
                metric_value=$(echo "$metric_data" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
            fi

            if [[ "$metric_value" != "N/A" ]]; then
                printf "  ✓ %-25s: %s\n" "$metric_display" "$metric_value"
            else
                printf "  ⚠ %-25s: No data\n" "$metric_display"
            fi
        else
            printf "  ⚠ %-25s: Not available\n" "$metric_display"
        fi
    done

    echo "=================================================="
    print_success "All monitoring metrics saved to individual JSON files"
}

# Function to fetch server parameters
fetch_server_parameters() {
    if [[ "$FETCH_PARAMETERS" != "true" ]]; then
        print_info "Parameter fetching is disabled (FETCH_PARAMETERS=false)"
        return 0
    fi

    print_info "Fetching MySQL server parameters..."

    # Fetch server parameters
    local parameters_json=$(az mysql flexible-server parameter list \
        --server-name "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json 2>/dev/null)

    if [[ -n "$parameters_json" && "$parameters_json" != "null" ]]; then
        echo "$parameters_json" > "${SERVER_NAME}_parameters.json"
        print_success "Server parameters saved to: ${SERVER_NAME}_parameters.json"

        # Count total parameters
        local param_count=$(echo "$parameters_json" | jq '. | length' 2>/dev/null || echo "0")
        print_info "Total parameters collected: $param_count"

        # Show some key parameters for verification
        echo ""
        echo "=================================================="
        echo "           KEY SERVER PARAMETERS"
        echo "=================================================="

        # Extract and display some important parameters
        local max_connections=$(echo "$parameters_json" | jq -r '.[] | select(.name=="max_connections") | .value' 2>/dev/null || echo "N/A")
        local innodb_buffer_pool_size=$(echo "$parameters_json" | jq -r '.[] | select(.name=="innodb_buffer_pool_size") | .value' 2>/dev/null || echo "N/A")
        local slow_query_log=$(echo "$parameters_json" | jq -r '.[] | select(.name=="slow_query_log") | .value' 2>/dev/null || echo "N/A")
        local long_query_time=$(echo "$parameters_json" | jq -r '.[] | select(.name=="long_query_time") | .value' 2>/dev/null || echo "N/A")
        local general_log=$(echo "$parameters_json" | jq -r '.[] | select(.name=="general_log") | .value' 2>/dev/null || echo "N/A")

        printf "%-25s: %s\n" "Max Connections" "$max_connections"
        printf "%-25s: %s\n" "InnoDB Buffer Pool Size" "$innodb_buffer_pool_size"
        printf "%-25s: %s\n" "Slow Query Log" "$slow_query_log"
        printf "%-25s: %s\n" "Long Query Time" "$long_query_time"
        printf "%-25s: %s\n" "General Log" "$general_log"

        echo "=================================================="
        print_info "Complete parameter list available in JSON file"

    else
        print_error "Failed to fetch server parameters"
        print_info "This might be due to insufficient permissions or server unavailability"
        return 1
    fi
}

# Function to organize JSON files into service directory
organize_json_files() {
    local service_dir="mysql-flexible-server-${SERVER_NAME}"
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local timestamped_dir="${service_dir}_${timestamp}"

    print_info "Organizing JSON files into directory: $timestamped_dir"

    # Create service directory
    if mkdir -p "$timestamped_dir"; then
        print_success "Created directory: $timestamped_dir"
    else
        print_error "Failed to create directory: $timestamped_dir"
        return 1
    fi

    # Create primary server subdirectory
    local primary_dir="$timestamped_dir/primary-server"
    if mkdir -p "$primary_dir"; then
        print_success "Created primary server directory: $primary_dir"
    else
        print_error "Failed to create primary server directory: $primary_dir"
        return 1
    fi

    # Move primary server JSON files to the primary-server subdirectory
    local files_moved=0

    # Move all JSON files that were actually created (dynamic discovery)
    print_info "Discovering and moving created JSON files..."

    # Find all JSON files that match the server name pattern (excluding replication info)
    local json_files_found=()
    while IFS= read -r -d '' file; do
        local filename=$(basename "$file")
        # Skip replication info file (handled separately)
        if [[ "$filename" != "${SERVER_NAME}_replication_info.json" ]]; then
            json_files_found+=("$filename")
        fi
    done < <(find . -maxdepth 1 -name "${SERVER_NAME}_*.json" -print0 2>/dev/null)

    print_info "Found ${#json_files_found[@]} JSON files to move"

    # Move discovered files (temporarily disable errexit for file operations)
    set +e  # Disable exit on error temporarily
    for json_file in "${json_files_found[@]}"; do
        if [[ -f "$json_file" ]]; then
            if mv "$json_file" "$primary_dir/" 2>/dev/null; then
                print_success "Moved primary file: $json_file -> $primary_dir/"
                ((files_moved++))
            else
                print_error "Failed to move primary file: $json_file"
                # Continue with other files instead of exiting
            fi
        fi
    done
    set -e  # Re-enable exit on error

    # Move replication info to main directory (not primary subdirectory)
    set +e  # Disable exit on error temporarily
    if [[ -f "${SERVER_NAME}_replication_info.json" ]]; then
        if mv "${SERVER_NAME}_replication_info.json" "$timestamped_dir/" 2>/dev/null; then
            print_success "Moved replication info: ${SERVER_NAME}_replication_info.json -> $timestamped_dir/"
            ((files_moved++))
        else
            print_error "Failed to move replication info file"
            # Continue instead of exiting
        fi
    fi
    set -e  # Re-enable exit on error

    # Organize replica files into numbered subdirectories
    organize_replica_files "$timestamped_dir"

    # Create a summary file with key information
    local summary_file="$timestamped_dir/summary.json"
    create_summary_json "$timestamped_dir" "$summary_file"

    print_success "Organized $files_moved JSON files into: $timestamped_dir"
    print_info "Summary file created: $summary_file"

    # Display directory contents
    echo ""
    echo "=================================================="
    echo "           ORGANIZED FILES"
    echo "=================================================="
    ls -la "$timestamped_dir"
    echo "=================================================="

    # Set global variable for directory name (to avoid output capture issues)
    OUTPUT_DIRECTORY="$timestamped_dir"
}

# Function to organize replica files into numbered subdirectories
organize_replica_files() {
    local base_dir="$1"
    local replica_count=0

    # Check if replication info exists to determine if there are replicas
    if [[ -f "$base_dir/${SERVER_NAME}_replication_info.json" ]]; then
        local replica_servers=""
        # Use safer jq command that won't fail if the structure is different
        if command -v jq >/dev/null 2>&1; then
            replica_servers=$(jq -r '.replica_servers[]?' "$base_dir/${SERVER_NAME}_replication_info.json" 2>/dev/null || echo "")
        fi

        if [[ -n "$replica_servers" ]]; then
            print_info "Organizing replica files into subdirectories..."

            while IFS= read -r replica_name; do
                if [[ -n "$replica_name" ]]; then
                    ((replica_count++))
                    local replica_dir="$base_dir/replica-node-$replica_count"

                    # Create replica subdirectory
                    if mkdir -p "$replica_dir"; then
                        print_success "Created replica directory: $replica_dir"

                        # Move replica-specific files
                        local replica_files=(
                            "${replica_name}_replica_details.json"
                            "${replica_name}_metrics_summary.json"
                            "${replica_name}_cpu_metrics.json"
                            "${replica_name}_replication_lag_metrics.json"
                        )

                        local replica_files_moved=0
                        for replica_file in "${replica_files[@]}"; do
                            if [[ -f "$replica_file" ]]; then
                                if mv "$replica_file" "$replica_dir/"; then
                                    print_success "Moved replica file: $replica_file -> $replica_dir/"
                                    ((replica_files_moved++))
                                else
                                    print_error "Failed to move replica file: $replica_file"
                                fi
                            fi
                        done

                        # Create a replica info file in the replica directory
                        cat > "$replica_dir/replica_info.json" << EOF
{
  "replica_name": "$replica_name",
  "replica_node_number": $replica_count,
  "primary_server": "$SERVER_NAME",
  "files_collected": $replica_files_moved,
  "collection_timestamp": "$(date -u +"%Y-%m-%d %H:%M:%S UTC")"
}
EOF
                        print_success "Created replica info file: $replica_dir/replica_info.json"

                    else
                        print_error "Failed to create replica directory: $replica_dir"
                    fi
                fi
            done <<< "$replica_servers"

            print_success "Organized $replica_count replica node(s) into subdirectories"
        else
            print_info "No replica servers found - creating standalone server structure"
        fi
    else
        print_info "No replication info available - assuming standalone server"
    fi
}

# Function to create a summary JSON file
create_summary_json() {
    local dir="$1"
    local summary_file="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S UTC')

    # Extract key information from the server details JSON
    local server_json="$dir/primary-server/${SERVER_NAME}_full_details.json"

    if [[ ! -f "$server_json" ]]; then
        print_error "Server details JSON not found for summary"
        return 1
    fi

    # Extract metrics averages
    local avg_cpu="N/A"
    local avg_memory="N/A"
    local avg_storage="N/A"
    local avg_connections="N/A"
    local avg_io="N/A"

    if [[ -f "$dir/primary-server/${SERVER_NAME}_cpu_metrics.json" ]]; then
        avg_cpu=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_cpu_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_memory_metrics.json" ]]; then
        avg_memory=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_memory_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_storage_metrics.json" ]]; then
        avg_storage=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_storage_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_connection_metrics.json" ]]; then
        avg_connections=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_connection_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_io_metrics.json" ]]; then
        avg_io=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_io_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    # Extract vCPU from SKU name for summary
    local sku_name=$(jq -r '.sku.name' "$server_json")
    local server_capacity="N/A"
    if [[ "$sku_name" != "null" && "$sku_name" != "N/A" ]]; then
        # Extract number from SKU name patterns like Standard_B1ms, Standard_D2ds_v4, etc.
        server_capacity=$(echo "$sku_name" | sed -E 's/.*[_]([BDEGM])([0-9]+).*/\2/' | head -1)
        # If extraction failed, try alternative pattern
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity=$(echo "$sku_name" | sed -E 's/.*([0-9]+).*/\1/' | head -1)
        fi
        # If still no valid number, set to N/A
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity="N/A"
        fi
    fi

    # Create summary JSON
    cat > "$summary_file" << EOF
{
  "report_metadata": {
    "timestamp": "$timestamp",
    "subscription_id": "$AZURE_SUBSCRIPTION_ID",
    "resource_group": "$RESOURCE_GROUP_NAME",
    "server_name": "$SERVER_NAME",
    "metrics_time_period": "$METRICS_TIME_PERIOD",
    "service_type": "Azure Database for MySQL - Flexible Server"
  },
  "server_summary": {
    "status": $(jq '.state' "$server_json"),
    "location": $(jq '.location' "$server_json"),
    "availability_zone": $(jq '.availabilityZone' "$server_json"),
    "mysql_version": $(jq '.version' "$server_json"),
    "fqdn": $(jq '.fullyQualifiedDomainName' "$server_json"),
    "pricing_tier": $(jq '.sku.tier' "$server_json"),
    "sku_name": $(jq '.sku.name' "$server_json"),
    "vcpu_capacity": "$server_capacity",
    "storage_size_gb": $(jq '.storage.storageSizeGb' "$server_json"),
    "storage_iops": $(jq '.storage.iops' "$server_json"),
    "backup_retention_days": $(jq '.backup.backupRetentionDays' "$server_json"),
    "high_availability_mode": $(jq '.highAvailability.mode' "$server_json")
  },
  "monitoring_summary": {
    "avg_cpu_usage_percent": "$avg_cpu",
    "avg_memory_usage_percent": "$avg_memory",
    "avg_storage_usage_percent": "$avg_storage",
    "avg_active_connections": "$avg_connections",
    "avg_io_consumption_percent": "$avg_io"
  },
  "data_files": {
    "primary_server_directory": "primary-server/",
    "replication_info": "${SERVER_NAME}_replication_info.json",
    "primary_server_files": {
      "server_details_full": "primary-server/${SERVER_NAME}_full_details.json",
      "cpu_metrics": "primary-server/${SERVER_NAME}_cpu_metrics.json",
      "memory_metrics": "primary-server/${SERVER_NAME}_memory_metrics.json",
      "storage_metrics": "primary-server/${SERVER_NAME}_storage_metrics.json",
      "connection_metrics": "primary-server/${SERVER_NAME}_connection_metrics.json",
      "io_metrics": "primary-server/${SERVER_NAME}_io_metrics.json",
      "network_in_metrics": "primary-server/${SERVER_NAME}_network_in_metrics.json",
      "network_out_metrics": "primary-server/${SERVER_NAME}_network_out_metrics.json",
      "replication_lag_metrics": "primary-server/${SERVER_NAME}_replication_lag_metrics.json",
      "aborted_connections_metrics": "primary-server/${SERVER_NAME}_aborted_connections_metrics.json",
      "queries_metrics": "primary-server/${SERVER_NAME}_queries_metrics.json",
      "server_parameters": "primary-server/${SERVER_NAME}_parameters.json"
    },
    "replica_directories": "replica-node-*/",
    "note": "Replica data is organized in replica-node-N subdirectories"
  }
}
EOF

    print_success "Summary JSON created with key metrics and file references"
}

# Function to check MySQL Flexible Server
check_mysql_flexible_server() {
    print_info "Checking MySQL Flexible Server: $SERVER_NAME"

    # Check if the server exists
    if az mysql flexible-server show --resource-group "$RESOURCE_GROUP_NAME" --name "$SERVER_NAME" &> /dev/null; then
        print_success "MySQL Flexible Server '$SERVER_NAME' found"

        # Get detailed server information
        get_detailed_server_info

        # Get monitoring metrics
        print_info "About to call get_monitoring_metrics function..."
        if get_monitoring_metrics; then
            print_success "Monitoring metrics collection completed"
        else
            print_error "Failed to collect monitoring metrics"
        fi

        # Fetch server parameters if enabled
        fetch_server_parameters

        # Discover and analyze replica servers
        discover_replica_servers

        # Organize JSON files into service directory
        organize_json_files

        # Create ZIP file if requested
        create_zip_file

    else
        print_error "MySQL Flexible Server '$SERVER_NAME' not found in resource group '$RESOURCE_GROUP_NAME'"
        print_info "Available MySQL Flexible Servers in resource group:"
        az mysql flexible-server list --resource-group "$RESOURCE_GROUP_NAME" --query "[].{Name:name, State:state, Location:location}" -o table
        exit 1
    fi
}

# Function to parse command line arguments
parse_arguments() {
    GENERATE_ZIP="false"
    CONFIG_FILE=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --generate-zip)
                GENERATE_ZIP="true"
                shift
                ;;
            --generate-zip=*)
                GENERATE_ZIP="${1#*=}"
                shift
                ;;
            -*)
                print_error "Unknown option: $1"
                print_info "Usage: $0 [--generate-zip] <config_file_path>"
                exit 1
                ;;
            *)
                if [[ -z "$CONFIG_FILE" ]]; then
                    CONFIG_FILE="$1"
                else
                    print_error "Multiple config files specified"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Validate generate_zip value
    if [[ "$GENERATE_ZIP" != "true" && "$GENERATE_ZIP" != "false" ]]; then
        print_error "Invalid --generate-zip value: $GENERATE_ZIP"
        print_info "--generate-zip must be 'true' or 'false'"
        exit 1
    fi
}

# Function to create ZIP file if requested
create_zip_file() {
    if [[ "$GENERATE_ZIP" != "true" ]]; then
        print_info "ZIP creation disabled (GENERATE_ZIP=false)"
        return 0
    fi

    if [[ -z "$OUTPUT_DIRECTORY" || ! -d "$OUTPUT_DIRECTORY" ]]; then
        print_error "Output directory not found for ZIP creation: $OUTPUT_DIRECTORY"
        return 1
    fi

    print_info "Creating ZIP file for directory: $OUTPUT_DIRECTORY"

    local zip_file="${OUTPUT_DIRECTORY}.zip"

    # Remove existing ZIP file if it exists
    if [[ -f "$zip_file" ]]; then
        print_info "Removing existing ZIP file: $zip_file"
        rm -f "$zip_file"
    fi

    if command -v zip &> /dev/null; then
        print_info "Using zip utility to create archive..."

        # Create ZIP archive with verbose output for debugging
        if zip -r "$zip_file" "$OUTPUT_DIRECTORY" 2>&1; then
            if [[ -f "$zip_file" ]]; then
                print_success "ZIP archive created successfully: $zip_file"

                # Get ZIP file size for reporting
                local zip_size
                if command -v du &> /dev/null; then
                    zip_size=$(du -h "$zip_file" 2>/dev/null | cut -f1)
                    print_info "ZIP file size: $zip_size"
                fi

                # Get file count in ZIP
                local file_count
                if command -v unzip &> /dev/null; then
                    file_count=$(unzip -l "$zip_file" 2>/dev/null | tail -1 | awk '{print $2}')
                    print_info "Files in ZIP: $file_count"
                fi

                # Remove original directory
                print_info "Removing original directory: $OUTPUT_DIRECTORY"
                if rm -rf "$OUTPUT_DIRECTORY"; then
                    print_success "Original directory removed successfully"
                    print_info "Final output: $zip_file"
                else
                    print_error "Failed to remove original directory: $OUTPUT_DIRECTORY"
                    print_warning "ZIP file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                print_error "ZIP file was not created successfully"
                return 1
            fi
        else
            print_error "Failed to create ZIP archive using zip utility"
            return 1
        fi
    elif command -v tar &> /dev/null; then
        print_info "ZIP utility not available, using tar with gzip compression..."

        local tar_file="${output_dir}.tar.gz"

        # Remove existing tar file if it exists
        if [[ -f "$tar_file" ]]; then
            print_info "Removing existing tar file: $tar_file"
            rm -f "$tar_file"
        fi

        if tar -czf "$tar_file" "$output_dir" 2>&1; then
            if [[ -f "$tar_file" ]]; then
                print_success "TAR.GZ archive created successfully: $tar_file"

                # Get file size
                local tar_size
                if command -v du &> /dev/null; then
                    tar_size=$(du -h "$tar_file" 2>/dev/null | cut -f1)
                    print_info "TAR.GZ file size: $tar_size"
                fi

                # Remove original directory
                print_info "Removing original directory: $output_dir"
                if rm -rf "$output_dir"; then
                    print_success "Original directory removed successfully"
                    print_info "Final output: $tar_file"
                else
                    print_error "Failed to remove original directory: $output_dir"
                    print_warning "TAR.GZ file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                print_error "TAR.GZ file was not created successfully"
                return 1
            fi
        else
            print_error "Failed to create TAR.GZ archive"
            return 1
        fi
    else
        print_warning "Neither zip nor tar utilities are available, skipping archive creation"
        print_info "Please install zip or tar to enable archive creation"
        print_info "Data is available in directory: $output_dir"
        return 1
    fi
}

# Main function
main() {
    local config_file="$1"

    print_info "Starting Azure MySQL Flexible Server validation script"
    print_info "=================================================="

    # Check if config file is provided
    if [[ -z "$config_file" ]]; then
        print_error "No configuration file provided"
        exit 1
    fi

    print_info "Generate ZIP: $GENERATE_ZIP"
    
    # Step 1: Check prerequisites
    check_prerequisites

    # Step 2: Validate and load configuration
    validate_config "$config_file"
    
    # Step 3: Check Azure login and subscription
    check_azure_login
    
    # Step 4: Validate resource group
    validate_resource_group
    
    # Step 5: Check MySQL Flexible Server
    check_mysql_flexible_server
    
    print_success "All validations completed successfully!"
    print_info "=================================================="
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    CONFIG_FILE=""
    GENERATE_ZIP_ARG=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --config-path)
                if [[ -n "$2" && "$2" != --* ]]; then
                    CONFIG_FILE="$2"
                    shift 2
                else
                    echo "Error: --config-path requires a file path argument"
                    exit 1
                fi
                ;;
            --generate-zip)
                if [[ -n "$2" && "$2" != --* ]]; then
                    case "$2" in
                        true|false)
                            GENERATE_ZIP_ARG="$2"
                            shift 2
                            ;;
                        *)
                            echo "Error: --generate-zip requires 'true' or 'false' as argument"
                            exit 1
                            ;;
                    esac
                else
                    echo "Error: --generate-zip requires 'true' or 'false' as argument"
                    exit 1
                fi
                ;;
            --help|-h)
                echo "Azure MySQL Flexible Server Monitoring Script"
                echo ""
                echo "Usage: $0 --config-path <config-file> --generate-zip <true|false>"
                echo ""
                echo "Required Arguments:"
                echo "  --config-path PATH    Path to configuration file"
                echo "  --generate-zip BOOL   Create ZIP archive (true/false)"
                echo ""
                echo "Optional Arguments:"
                echo "  --help, -h           Show this help message"
                echo ""
                echo "Examples:"
                echo "  $0 --config-path test-config.conf --generate-zip true"
                echo "  $0 --config-path test-config.conf --generate-zip false"
                echo ""
                echo "The configuration file should contain:"
                echo "  AZURE_SUBSCRIPTION_ID=\"your-subscription-id\""
                echo "  RESOURCE_GROUP_NAME=\"your-resource-group\""
                echo "  SERVER_NAME=\"your-mysql-server-name\""
                echo "  METRICS_TIME_PERIOD=\"1h\"  # Optional: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
                echo "  FETCH_PARAMETERS=\"true\"   # Optional: true/false"
                exit 0
                ;;
            *)
                echo "Error: Unknown argument '$1'"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$CONFIG_FILE" ]]; then
        echo "Error: Missing required argument --config-path"
        echo "Usage: $0 --config-path <config-file> --generate-zip <true|false>"
        echo "Use --help for more information"
        exit 1
    fi

    if [[ -z "$GENERATE_ZIP_ARG" ]]; then
        echo "Error: Missing required argument --generate-zip"
        echo "Usage: $0 --config-path <config-file> --generate-zip <true|false>"
        echo "Use --help for more information"
        exit 1
    fi

    # Validate config file exists
    if [[ ! -f "$CONFIG_FILE" ]]; then
        echo "Error: Configuration file '$CONFIG_FILE' not found"
        exit 1
    fi

    # Set ZIP generation flag
    export GENERATE_ZIP="$GENERATE_ZIP_ARG"

    # Execute main function with provided config file
    main "$CONFIG_FILE"
fi
