[{"autoUpgradeMinorVersion": false, "enableAutomaticUpgrade": false, "forceUpdateTag": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/virtualMachines/clonrerefresh001-jnovbwn-server1_0/extensions/AzureMonitorLinuxAgent", "instanceView": null, "location": "eastus", "name": "AzureMonitorLinuxAgent", "protectedSettings": null, "protectedSettingsFromKeyVault": null, "provisionAfterExtensions": null, "provisioningState": "Succeeded", "publisher": "Microsoft.Azure.Monitor", "resourceGroup": "dev-byoa-shared-2-rg", "settings": {"stopOnMultipleConnections": true, "workspaceId": "3bf81bf0-d296-403b-b3a7-913cffd345ac"}, "suppressFailures": null, "tags": null, "type": "Microsoft.Compute/virtualMachines/extensions", "typeHandlerVersion": "1.31", "typePropertiesType": "AzureMonitorLinuxAgent"}, {"autoUpgradeMinorVersion": true, "enableAutomaticUpgrade": null, "forceUpdateTag": "d7241b02-705c-4621-9a56-a1f11bdeb596", "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/virtualMachines/clonrerefresh001-jnovbwn-server1_0/extensions/MDE.Linux", "instanceView": null, "location": "eastus", "name": "MDE.Linux", "protectedSettings": null, "protectedSettingsFromKeyVault": null, "provisionAfterExtensions": null, "provisioningState": "Succeeded", "publisher": "Microsoft.Azure.AzureDefenderForServers", "resourceGroup": "dev-byoa-shared-2-rg", "settings": {"autoUpdate": true, "azureResourceId": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/DEV-BYOA-SHARED-2-RG/providers/Microsoft.Compute/virtualMachines/clonrerefresh001-jnovbwn-server1_0", "forceReOnboarding": false, "vNextEnabled": false}, "suppressFailures": null, "tags": null, "type": "Microsoft.Compute/virtualMachines/extensions", "typeHandlerVersion": "1.0", "typePropertiesType": "MDE.Linux"}]