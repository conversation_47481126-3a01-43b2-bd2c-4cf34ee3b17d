{"additionalCapabilities": null, "applicationProfile": null, "availabilitySet": null, "billingProfile": null, "capacityReservation": null, "diagnosticsProfile": null, "etag": "\"4\"", "evictionPolicy": null, "extendedLocation": null, "extensionsTimeBudget": null, "hardwareProfile": {"vmSize": "Standard_E4as_v6", "vmSizeProperties": null}, "host": null, "hostGroup": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/virtualMachines/clonrerefresh001-jnovbwn-server1_0", "identity": {"principalId": null, "tenantId": null, "type": "UserAssigned", "userAssignedIdentities": {"/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.ManagedIdentity/userAssignedIdentities/tessell-vm-msi-devshared2": {"clientId": "41e658af-1cbd-4f71-b5ad-aa1d88634f20", "principalId": "058de0dd-7df6-4d64-a6ec-67804cab0f31"}}}, "instanceView": null, "licenseType": null, "location": "eastus", "managedBy": null, "name": "clonrerefresh001-jnovbwn-server1_0", "networkProfile": {"networkApiVersion": null, "networkInterfaceConfigurations": null, "networkInterfaces": [{"deleteOption": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Network/networkInterfaces/clonrerefresh001-jnovbwn-server1_0-nic", "primary": true, "resourceGroup": "dev-byoa-shared-2-rg"}, {"deleteOption": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Network/networkInterfaces/clonrerefresh001-jnovbwn-server1_0-nic2", "primary": false, "resourceGroup": "dev-byoa-shared-2-rg"}]}, "osProfile": null, "plan": null, "platformFaultDomain": null, "priority": null, "provisioningState": "Succeeded", "proximityPlacementGroup": null, "resourceGroup": "dev-byoa-shared-2-rg", "resources": [{"autoUpgradeMinorVersion": false, "enableAutomaticUpgrade": false, "forceUpdateTag": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/virtualMachines/clonrerefresh001-jnovbwn-server1_0/extensions/AzureMonitorLinuxAgent", "instanceView": null, "location": "eastus", "name": "AzureMonitorLinuxAgent", "protectedSettings": null, "protectedSettingsFromKeyVault": null, "provisionAfterExtensions": null, "provisioningState": "Succeeded", "publisher": "Microsoft.Azure.Monitor", "resourceGroup": "dev-byoa-shared-2-rg", "settings": {"stopOnMultipleConnections": true, "workspaceId": "3bf81bf0-d296-403b-b3a7-913cffd345ac"}, "suppressFailures": null, "tags": null, "type": "Microsoft.Compute/virtualMachines/extensions", "typeHandlerVersion": "1.31", "typePropertiesType": "AzureMonitorLinuxAgent"}, {"autoUpgradeMinorVersion": true, "enableAutomaticUpgrade": null, "forceUpdateTag": "d7241b02-705c-4621-9a56-a1f11bdeb596", "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/virtualMachines/clonrerefresh001-jnovbwn-server1_0/extensions/MDE.Linux", "instanceView": null, "location": "eastus", "name": "MDE.Linux", "protectedSettings": null, "protectedSettingsFromKeyVault": null, "provisionAfterExtensions": null, "provisioningState": "Succeeded", "publisher": "Microsoft.Azure.AzureDefenderForServers", "resourceGroup": "dev-byoa-shared-2-rg", "settings": {"autoUpdate": true, "azureResourceId": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/DEV-BYOA-SHARED-2-RG/providers/Microsoft.Compute/virtualMachines/clonrerefresh001-jnovbwn-server1_0", "forceReOnboarding": false, "vNextEnabled": false}, "suppressFailures": null, "tags": null, "type": "Microsoft.Compute/virtualMachines/extensions", "typeHandlerVersion": "1.0", "typePropertiesType": "MDE.Linux"}], "scheduledEventsPolicy": null, "scheduledEventsProfile": null, "securityProfile": null, "storageProfile": {"dataDisks": [{"caching": "ReadWrite", "createOption": "Attach", "deleteOption": "<PERSON><PERSON>", "detachOption": null, "diskIopsReadWrite": null, "diskMBpsReadWrite": null, "diskSizeGb": 50, "image": null, "lun": 1, "managedDisk": {"diskEncryptionSet": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/disks/clonrerefresh001-jnovbwn-server1_0-tessell-lib-abxgi", "resourceGroup": "dev-byoa-shared-2-rg", "securityProfile": null, "storageAccountType": "Premium_LRS"}, "name": "clonrerefresh001-jnovbwn-server1_0-tessell-lib-abxgi", "sourceResource": null, "toBeDetached": false, "vhd": null, "writeAcceleratorEnabled": false}, {"caching": "ReadWrite", "createOption": "Attach", "deleteOption": "<PERSON><PERSON>", "detachOption": null, "diskIopsReadWrite": null, "diskMBpsReadWrite": null, "diskSizeGb": 64, "image": null, "lun": 2, "managedDisk": {"diskEncryptionSet": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/disks/clonrerefresh001-jnovbwn-server1_0-dbsoft-oxaeh", "resourceGroup": "dev-byoa-shared-2-rg", "securityProfile": null, "storageAccountType": "Premium_LRS"}, "name": "clonrerefresh001-jnovbwn-server1_0-dbsoft-oxaeh", "sourceResource": null, "toBeDetached": false, "vhd": null, "writeAcceleratorEnabled": false}, {"caching": "None", "createOption": "Attach", "deleteOption": "<PERSON><PERSON>", "detachOption": null, "diskIopsReadWrite": null, "diskMBpsReadWrite": null, "diskSizeGb": 25, "image": null, "lun": 3, "managedDisk": {"diskEncryptionSet": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/disks/clonrerefresh001-jnovbwn-server1_0-data-archivecxhvz", "resourceGroup": "dev-byoa-shared-2-rg", "securityProfile": null, "storageAccountType": "PremiumV2_LRS"}, "name": "clonrerefresh001-jnovbwn-server1_0-data-archivecxhvz", "sourceResource": null, "toBeDetached": false, "vhd": null, "writeAcceleratorEnabled": false}, {"caching": "None", "createOption": "Attach", "deleteOption": "<PERSON><PERSON>", "detachOption": null, "diskIopsReadWrite": null, "diskMBpsReadWrite": null, "diskSizeGb": 100, "image": null, "lun": 4, "managedDisk": {"diskEncryptionSet": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/disks/clonrerefresh001-jnovbwn-server1_0-data-disk-tyuks-0", "resourceGroup": "dev-byoa-shared-2-rg", "securityProfile": null, "storageAccountType": "PremiumV2_LRS"}, "name": "clonrerefresh001-jnovbwn-server1_0-data-disk-tyuks-0", "sourceResource": null, "toBeDetached": false, "vhd": null, "writeAcceleratorEnabled": false}], "diskControllerType": "NVMe", "imageReference": null, "osDisk": {"caching": "ReadWrite", "createOption": "Attach", "deleteOption": "<PERSON><PERSON>", "diffDiskSettings": null, "diskSizeGb": 64, "encryptionSettings": null, "image": null, "managedDisk": {"diskEncryptionSet": null, "id": "/subscriptions/a78d329d-3056-4e59-a5e9-0cf78f204774/resourceGroups/dev-byoa-shared-2-rg/providers/Microsoft.Compute/disks/clonrerefresh001-jnovbwn-server1_0-os-disk", "resourceGroup": "dev-byoa-shared-2-rg", "securityProfile": null, "storageAccountType": "Premium_LRS"}, "name": "clonrerefresh001-jnovbwn-server1_0-os-disk", "osType": "Linux", "vhd": null, "writeAcceleratorEnabled": false}}, "tags": {"TESSELL_COMPUTE_RESOURCE_ID": "79b2359f-2bbd-4ed3-afa7-72057d7416ea", "TESSELL_COMPUTE_TYPE": "DBSERVICE", "TESSELL_DBSERVICE_ID": "b0ec3374-1e30-4ab8-afdd-d1a471d06d8e", "TESSELL_DBSERVICE_NAME": "my-clone-cca2545b", "TESSELL_ENTITY_ID": "b0ec3374-1e30-4ab8-afdd-d1a471d06d8e", "TESSELL_ENVIRONMENT": "dev", "TESSELL_REFERENCE_ID": "d072e8c3f744b155b4cb1e0139e758e4", "TESSELL_SUBSCRIPTION_ID": "azure-byoa-random", "TESSELL_TENANT_ID": "clonrerefresh001", "TESSELL_USER_ID": "<EMAIL>", "deploymentId": "79f26e06-8bcf-4be7-9c88-0e66c5e629e2"}, "timeCreated": "2025-07-15T06:04:21.126805+00:00", "type": "Microsoft.Compute/virtualMachines", "userData": null, "virtualMachineScaleSet": null, "vmId": "49640e7d-fc94-4974-bde0-dacb65bd53c9", "zones": ["1"]}