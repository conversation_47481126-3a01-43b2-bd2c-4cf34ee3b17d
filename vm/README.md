# Azure Virtual Machine Monitoring Script

Enterprise-grade monitoring script for Azure Virtual Machines with comprehensive metrics collection, resource analysis, and professional logging.

## ✨ **Key Features**

- **🔧 JSON Configuration** - Modern, readable configuration format with auto-detection
- **📊 Dynamic Metrics Collection** - Configurable metrics list for future-proof monitoring
- **🕐 UTC + Local Timestamps** - Professional logging with timezone consistency
- **🖥️ VM Configuration Analysis** - Complete VM details, extensions, and settings
- **💾 Storage Monitoring** - OS and data disk information with performance metrics
- **🌐 Network Analysis** - Network interfaces, public IPs, and traffic metrics
- **📁 Organized Output** - Structured JSON files with summary reports
- **📦 ZIP Archive Support** - Compressed output for easy sharing
- **🎨 Enterprise Logging** - Color-coded output with detailed timestamps
- **🛡️ Error Resilience** - Robust error handling that continues on individual failures
- **🔄 Backward Compatibility** - Supports legacy .conf files

## 🚀 **Quick Start**

### **1. Prerequisites**
```bash
# Install Azure CLI
brew install azure-cli  # macOS
# or
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash  # Ubuntu/Debian

# Install jq for JSON processing
brew install jq  # macOS
# or  
sudo apt-get install jq  # Ubuntu/Debian

# Login to Azure
az login
```

### **2. Configuration**

#### **JSON Configuration (Recommended)**
Create a `config.json` file:
```json
{
  "azure": {
    "subscription_id": "your-subscription-id",
    "resource_group": "your-resource-group",
    "vm_name": "your-vm-name"
  },
  "monitoring": {
    "metrics_time_period": "1h",
    "_supported_values": "1h, 6h, 12h, 1d, 3d, 7d, 30d"
  },
  "collection": {
    "collect_metrics": true,
    "metrics_list": [
      "Percentage CPU",
      "Available Memory Bytes",
      "Disk Read Bytes",
      "Disk Write Bytes",
      "Network In Total",
      "Network Out Total",
      "OS Disk Read Bytes/sec",
      "OS Disk Write Bytes/sec"
    ]
  }
}
```

#### **Legacy .conf Configuration (Still Supported)**
Create a configuration file (e.g., `production.conf`):
```bash
# Azure Configuration
AZURE_SUBSCRIPTION_ID="your-subscription-id"
RESOURCE_GROUP_NAME="your-resource-group"
VM_NAME="your-vm-name"

# Monitoring Configuration
METRICS_TIME_PERIOD="1h"  # Options: 1h, 6h, 12h, 1d, 3d, 7d, 30d

# Feature Configuration
COLLECT_METRICS="true"    # Set to "false" to skip metrics collection
FETCH_EXTENSIONS="true"   # Set to "false" to skip VM extensions collection
FETCH_DISKS="true"        # Set to "false" to skip disk information collection
FETCH_NETWORK="true"      # Set to "false" to skip network information collection
```

### **3. Usage**
```bash
# With JSON configuration (recommended) - ZIP generated by default
bash azure_vm_check.sh --config-path config.json

# With legacy .conf configuration - ZIP generated by default
bash azure_vm_check.sh --config-path production.conf

# Disable ZIP generation (keep directory structure)
bash azure_vm_check.sh --config-path config.json --generate-zip false

# Get help
bash azure_vm_check.sh --help
```

## 🎯 **Use Cases**

### **1. Quick Configuration Check (Fastest - ~5K ZIP)**
```json
{
  "collection": {
    "collect_metrics": false,
    "metrics_list": []
  }
}
```

### **2. Performance Monitoring (~15K ZIP)**
```json
{
  "collection": {
    "collect_metrics": true,
    "metrics_list": ["Percentage CPU", "Available Memory Bytes", "Network In Total"]
  }
}
```

### **3. Complete Analysis (~30K ZIP)**
```json
{
  "collection": {
    "collect_metrics": true,
    "metrics_list": [/* all available metrics */]
  }
}
```

## 📊 **Professional Logging Output**

The script provides enterprise-grade logging with UTC timestamps:

```bash
[2025-07-14 16:12:50 (UTC: 2025-07-14 10:42:50)] [INFO] Starting Azure Virtual Machine monitoring script
[2025-07-14 16:12:50 (UTC: 2025-07-14 10:42:50)] [SUCCESS] All required tools are available
[2025-07-14 16:12:50 (UTC: 2025-07-14 10:42:50)] [INFO] Reading configuration from: production.conf
[2025-07-14 16:12:50 (UTC: 2025-07-14 10:42:50)] [SUCCESS] Configuration validated successfully
[2025-07-14 16:12:50 (UTC: 2025-07-14 10:42:50)] [INFO] Subscription ID: 207a84e2-208b-4809-baab-e94a043914f5
[2025-07-14 16:12:50 (UTC: 2025-07-14 10:42:50)] [INFO] Resource Group: production-rg
[2025-07-14 16:12:50 (UTC: 2025-07-14 10:42:50)] [INFO] VM Name: production-vm
```

## 📁 **Output Structure**

### **Directory Organization:**
```
azure-vm-myvm_20250714_161250/
├── summary.json                           # 📋 Consolidated summary report
├── vm-info/                               # 🖥️ Virtual Machine information
│   ├── myvm_full_details.json            # Complete VM configuration
│   └── myvm_extensions.json              # Installed VM extensions
├── metrics/                               # 📊 Performance metrics
│   ├── myvm_cpu_metrics.json             # CPU utilization metrics
│   ├── myvm_memory_metrics.json          # Memory usage metrics
│   ├── myvm_disk_read_ops_metrics.json   # Disk read operations
│   └── myvm_disk_write_ops_metrics.json  # Disk write operations
├── network/                               # 🌐 Network information
│   ├── myvm_network_interfaces.json      # Network interface details
│   ├── myvm_network_in_metrics.json      # Network ingress metrics
│   └── myvm_network_out_metrics.json     # Network egress metrics
└── storage/                               # 💾 Storage information
    ├── myvm_disk_info.json               # Disk configuration
    └── myvm_os_disk_details.json         # OS disk details
```

## 📊 **Collected Metrics**

### **Core Performance Metrics:**
- **CPU Utilization** - Average CPU usage percentage over time period
- **Available Memory** - Available memory in GB
- **Disk Operations** - Read and write operations per second
- **Network Traffic** - Total ingress and egress bytes
- **Storage Performance** - Disk I/O metrics and utilization

### **VM Configuration Data:**
- **VM Details** - Size, location, OS type, provisioning state
- **Operating System** - Publisher, offer, SKU, version information
- **Power State** - Current VM power status
- **VM Extensions** - Installed extensions with versions and states
- **Storage Configuration** - OS disk and data disk details
- **Network Configuration** - NICs, private IPs, public IPs

### **Advanced Monitoring:**
- **Disk Performance** - Read/write operations per second
- **Network Throughput** - Detailed ingress/egress metrics
- **Extension Status** - Health and provisioning state of extensions
- **Storage Details** - Disk types, tiers, and sizes
- **Network Topology** - Interface configuration and IP assignments

## 🔧 **Advanced Configuration**

### **Time Period Options:**
```bash
METRICS_TIME_PERIOD="1h"   # Last 1 hour (default)
METRICS_TIME_PERIOD="6h"   # Last 6 hours  
METRICS_TIME_PERIOD="12h"  # Last 12 hours
METRICS_TIME_PERIOD="1d"   # Last 1 day
METRICS_TIME_PERIOD="3d"   # Last 3 days
METRICS_TIME_PERIOD="7d"   # Last 7 days
METRICS_TIME_PERIOD="30d"  # Last 30 days
```

### **Feature Control:**
```bash
FETCH_EXTENSIONS="true"   # Collect VM extensions information
FETCH_DISKS="true"        # Collect disk configuration and details
FETCH_NETWORK="true"      # Collect network interfaces and configuration
```

## 🛠️ **Troubleshooting**

### **Common Issues:**
1. **Azure CLI not found** - Install Azure CLI using prerequisites
2. **jq not found** - Install jq for JSON processing
3. **Permission denied** - Ensure proper Azure RBAC permissions
4. **VM not found** - Verify VM name and resource group
5. **Subscription access** - Check subscription ID and access rights
6. **Metrics not available** - Some VMs may not have all metrics enabled

### **Error Handling:**
The script includes robust error handling:
- **Continues on individual metric failures** - Won't stop if one metric fails
- **Graceful file operation handling** - Handles file move failures safely
- **Clear error messages** - Detailed logging for troubleshooting
- **Validation at each step** - Comprehensive input validation

## 📈 **Enterprise Benefits**

- **Global Team Coordination** - UTC timestamps for multi-timezone teams
- **Audit Compliance** - Comprehensive logging with timestamps
- **Performance Monitoring** - Historical metrics for trend analysis  
- **Capacity Planning** - CPU, memory, disk, and network utilization data
- **Configuration Management** - Complete VM and extension documentation
- **Incident Response** - Detailed system state capture
- **Cost Optimization** - Resource utilization analysis for rightsizing

Perfect for **production environments**, **compliance audits**, and **performance optimization**! 🌟
