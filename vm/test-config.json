{"azure": {"subscription_id": "a78d329d-3056-4e59-a5e9-0cf78f204774", "resource_group": "dev-byoa-shared-2-rg", "vm_name": "datapipeline002-xnsnwnf-server1_0"}, "monitoring": {"metrics_time_period": "30d", "_supported_values": "1h, 6h, 12h, 1d, 3d, 7d, 30d"}, "collection": {"fetch_extensions": true, "fetch_disks": true, "fetch_network": true, "collect_metrics": false, "metrics_list": ["Percentage CPU", "Available Memory Bytes", "Disk Read Bytes", "Disk Write Bytes", "Network In Total", "Network Out Total", "Disk Read Operations/Sec", "Disk Write Operations/Sec", "CPU Credits Remaining", "CPU Credits Consumed", "Data Disk Read Bytes/sec", "Data Disk Write Bytes/sec", "Data Disk Read Operations/Sec", "Data Disk Write Operations/Sec", "OS Disk Read Bytes/sec", "OS Disk Write Bytes/sec", "OS Disk Read Operations/Sec", "OS Disk Write Operations/Sec", "Inbound Flows", "Outbound Flows", "Inbound Flows Maximum Creation Rate", "Outbound Flows Maximum Creation Rate"]}}