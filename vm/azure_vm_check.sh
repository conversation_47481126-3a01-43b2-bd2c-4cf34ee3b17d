#!/bin/bash

# Azure Virtual Machine Monitoring Script
# Version: 1.0
# Description: Comprehensive monitoring and metrics collection for Azure Virtual Machines
#              Supports VM configuration analysis and performance metrics collection
#
# USAGE:
#   bash azure_vm_check.sh --config-path <config-file> [--generate-zip <true|false>]
#
# EXAMPLES:
#   # JSON configuration (recommended) - ZIP generated by default
#   bash azure_vm_check.sh --config-path config.json
#
#   # Legacy .conf configuration - ZIP generated by default
#   bash azure_vm_check.sh --config-path config.conf
#
#   # Disable ZIP generation (keep directory structure)
#   bash azure_vm_check.sh --config-path config.json --generate-zip false
#
# JSON CONFIGURATION FORMAT:
#   {
#     "azure": {
#       "subscription_id": "your-subscription-id",
#       "resource_group": "your-resource-group",
#       "vm_name": "your-vm-name"
#     },
#     "monitoring": {
#       "metrics_time_period": "1h"
#     },
#     "collection": {
#       "collect_metrics": true,
#       "metrics_list": ["Percentage CPU", "Available Memory Bytes", "Network In Total"]
#     }
#   }
#
# LEGACY .CONF FORMAT (still supported):
#   AZURE_SUBSCRIPTION_ID="your-subscription-id"
#   RESOURCE_GROUP_NAME="your-resource-group"
#   VM_NAME="your-vm-name"
#   METRICS_TIME_PERIOD="1h"
#   COLLECT_METRICS="true"

# Note: Removed -e to allow graceful error handling and continuation

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get timestamp with both local and UTC time
get_timestamp() {
    local local_time=$(date +"%Y-%m-%d %H:%M:%S")
    local utc_time=$(date -u +"%Y-%m-%d %H:%M:%S")
    echo "${local_time} (UTC: ${utc_time})"
}

# Function to print colored output with timestamps
log_info() {
    echo -e "${BLUE}[$(get_timestamp)] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(get_timestamp)] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(get_timestamp)] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(get_timestamp)] [ERROR]${NC} $1"
}

# Function to execute Azure CLI commands safely with error handling
safe_az_command() {
    local command_description="$1"
    shift
    local az_command=("$@")

    log_info "Executing: $command_description" >&2

    local output
    local exit_code

    # Execute the command and capture both output and exit code
    if output=$("${az_command[@]}" 2>&1); then
        exit_code=0
        echo "$output"
        return 0
    else
        exit_code=$?
        log_error "Failed: $command_description" >&2
        log_error "Command: ${az_command[*]}" >&2
        log_error "Error output: $output" >&2
        log_warning "Continuing with next operation..." >&2
        # Return empty output to avoid jq parse errors
        echo ""
        return $exit_code
    fi
}

# Function to check if required tools are installed
check_prerequisites() {
    local missing_tools=()

    # Check Azure CLI
    if ! command -v az &> /dev/null; then
        missing_tools+=("Azure CLI")
        log_error "Azure CLI is not installed."
        log_info "Install: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    fi

    # Check jq for JSON parsing
    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
        log_error "jq is not installed (required for JSON parsing)."
        log_info "Install jq:"
        log_info "  macOS: brew install jq"
        log_info "  Ubuntu/Debian: sudo apt-get install jq"
        log_info "  CentOS/RHEL: sudo yum install jq"
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi

    log_success "All required tools are available"
}

# Function to validate time period format
validate_time_period() {
    local time_period="$1"

    # Valid time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d
    case "$time_period" in
        "1h"|"6h"|"12h"|"1d"|"3d"|"7d"|"30d")
            log_success "Valid time period: $time_period"
            ;;
        *)
            log_error "Invalid time period: $time_period"
            log_info "Supported time periods: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
            log_info "Examples: '1h' = 1 hour, '1d' = 1 day, '7d' = 7 days"
            exit 1
            ;;
    esac
}

# Function to load JSON configuration file
load_json_config() {
    local json_file="$1"

    # Check if jq is available
    if ! command -v jq >/dev/null 2>&1; then
        log_error "jq is required to parse JSON configuration files"
        log_info "Please install jq or use .conf format instead"
        exit 1
    fi

    # Validate JSON syntax
    if ! jq empty "$json_file" 2>/dev/null; then
        log_error "Invalid JSON syntax in config file: $json_file"
        exit 1
    fi

    # Extract configuration values from JSON
    AZURE_SUBSCRIPTION_ID=$(jq -r '.azure.subscription_id // empty' "$json_file")
    RESOURCE_GROUP_NAME=$(jq -r '.azure.resource_group // empty' "$json_file")
    VM_NAME=$(jq -r '.azure.vm_name // empty' "$json_file")

    METRICS_TIME_PERIOD=$(jq -r '.monitoring.metrics_time_period // "1h"' "$json_file")

    FETCH_EXTENSIONS=$(jq -r '.collection.fetch_extensions // true' "$json_file")
    FETCH_DISKS=$(jq -r '.collection.fetch_disks // true' "$json_file")
    FETCH_NETWORK=$(jq -r '.collection.fetch_network // true' "$json_file")
    COLLECT_METRICS=$(jq -r '.collection.collect_metrics // true' "$json_file")

    # Convert metrics_list array to comma-separated string
    local metrics_array=$(jq -r '.collection.metrics_list[]?' "$json_file" 2>/dev/null)
    if [[ -n "$metrics_array" ]]; then
        METRICS_LIST=$(echo "$metrics_array" | tr '\n' ',' | sed 's/,$//')
    else
        METRICS_LIST=""
    fi

    log_info "Successfully loaded JSON configuration"
}

# Function to validate config file and extract values
validate_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "Config file '$config_file' not found"
        exit 1
    fi
    
    log_info "Reading configuration from: $config_file"

    # Determine config file format and load accordingly
    if [[ "$config_file" == *.json ]]; then
        load_json_config "$config_file"
    else
        # Source the config file (legacy .conf format)
        source "$config_file"
    fi
    
    # Validate required variables
    if [[ -z "$AZURE_SUBSCRIPTION_ID" ]]; then
        log_error "AZURE_SUBSCRIPTION_ID not found in config file"
        exit 1
    fi

    if [[ -z "$RESOURCE_GROUP_NAME" ]]; then
        log_error "RESOURCE_GROUP_NAME not found in config file"
        exit 1
    fi

    if [[ -z "$VM_NAME" ]]; then
        log_error "VM_NAME not found in config file"
        exit 1
    fi

    # Set default values for optional parameters
    METRICS_TIME_PERIOD="${METRICS_TIME_PERIOD:-1h}"
    FETCH_EXTENSIONS="${FETCH_EXTENSIONS:-true}"
    FETCH_DISKS="${FETCH_DISKS:-true}"
    FETCH_NETWORK="${FETCH_NETWORK:-true}"
    COLLECT_METRICS="${COLLECT_METRICS:-true}"
    METRICS_LIST="${METRICS_LIST:-Percentage CPU,Available Memory Bytes,Disk Read Bytes,Disk Write Bytes,Network In Total,Network Out Total}"

    # Validate time period format
    validate_time_period "$METRICS_TIME_PERIOD"

    # Validate boolean settings
    for setting in FETCH_EXTENSIONS FETCH_DISKS FETCH_NETWORK COLLECT_METRICS; do
        local value="${!setting}"
        if [[ "$value" != "true" && "$value" != "false" ]]; then
            log_error "Invalid $setting value: $value"
            log_info "$setting must be 'true' or 'false'"
            exit 1
        fi
    done

    log_success "Configuration validated successfully"
    log_info "Subscription ID: $AZURE_SUBSCRIPTION_ID"
    log_info "Resource Group: $RESOURCE_GROUP_NAME"
    log_info "VM Name: $VM_NAME"
    log_info "Metrics Time Period: $METRICS_TIME_PERIOD"
    log_info "Collect Metrics: $COLLECT_METRICS"
    log_info "Fetch Extensions: $FETCH_EXTENSIONS"
    log_info "Fetch Disks: $FETCH_DISKS"
    log_info "Fetch Network: $FETCH_NETWORK"
}

# Function to check current Azure login status
check_azure_login() {
    log_info "Checking Azure login status..."
    
    # Check if user is logged in
    if ! az account show &> /dev/null; then
        log_error "Not logged into Azure. Please run 'az login' first."
        exit 1
    fi
    
    # Get current subscription
    local current_subscription=$(az account show --query "id" -o tsv 2>/dev/null)
    
    if [[ -z "$current_subscription" ]]; then
        log_error "Unable to retrieve current subscription information"
        exit 1
    fi
    
    log_success "Currently logged into Azure"
    log_info "Current subscription: $current_subscription"
    
    # Check if the required subscription is available
    if ! az account show --subscription "$AZURE_SUBSCRIPTION_ID" &> /dev/null; then
        log_error "Subscription '$AZURE_SUBSCRIPTION_ID' is not available or accessible"
        log_info "Available subscriptions:"
        az account list --query "[].{Name:name, SubscriptionId:id, State:state}" -o table
        exit 1
    fi
    
    # Set the subscription if it's different from current
    if [[ "$current_subscription" != "$AZURE_SUBSCRIPTION_ID" ]]; then
        log_info "Switching to subscription: $AZURE_SUBSCRIPTION_ID"
        if az account set --subscription "$AZURE_SUBSCRIPTION_ID"; then
            log_success "Successfully switched to subscription: $AZURE_SUBSCRIPTION_ID"
        else
            log_error "Failed to switch to subscription: $AZURE_SUBSCRIPTION_ID"
            exit 1
        fi
    else
        log_success "Already using the correct subscription"
    fi
}

# Function to validate resource group
validate_resource_group() {
    log_info "Validating resource group: $RESOURCE_GROUP_NAME"
    
    if az group show --name "$RESOURCE_GROUP_NAME" &> /dev/null; then
        log_success "Resource group '$RESOURCE_GROUP_NAME' exists"
        
        # Get resource group details
        local rg_location=$(az group show --name "$RESOURCE_GROUP_NAME" --query "location" -o tsv)
        log_info "Resource group location: $rg_location"
    else
        log_error "Resource group '$RESOURCE_GROUP_NAME' does not exist in subscription '$AZURE_SUBSCRIPTION_ID'"
        log_info "Available resource groups:"
        az group list --query "[].{Name:name, Location:location}" -o table
        exit 1
    fi
}

# Function to convert time period to hours
convert_time_period_to_hours() {
    local period="$1"
    case "$period" in
        "1h") echo "1" ;;
        "6h") echo "6" ;;
        "12h") echo "12" ;;
        "1d") echo "24" ;;
        "3d") echo "72" ;;
        "7d") echo "168" ;;
        "30d") echo "720" ;;
        *) echo "1" ;;
    esac
}

# Function to check if VM exists and get basic info
check_vm_exists() {
    log_info "Checking Virtual Machine: $VM_NAME"

    # Check if the VM exists
    if az vm show --resource-group "$RESOURCE_GROUP_NAME" --name "$VM_NAME" &> /dev/null; then
        log_success "Virtual Machine '$VM_NAME' found"
        return 0
    else
        log_error "Virtual Machine '$VM_NAME' not found in resource group '$RESOURCE_GROUP_NAME'"
        log_info "Available Virtual Machines in resource group:"
        az vm list --resource-group "$RESOURCE_GROUP_NAME" --query "[].{Name:name, PowerState:powerState, Location:location}" -o table
        exit 1
    fi
}

# Function to get detailed VM information
get_detailed_vm_info() {
    log_info "Fetching comprehensive VM information..."

    # Get full VM details
    local vm_json=$(az vm show --resource-group "$RESOURCE_GROUP_NAME" --name "$VM_NAME" --output json)

    # Save full details to file
    echo "$vm_json" > "${VM_NAME}_full_details.json"
    log_success "Full VM details saved to: ${VM_NAME}_full_details.json"

    # Extract detailed information
    local vm_location=$(echo "$vm_json" | jq -r '.location // "N/A"')
    local vm_size=$(echo "$vm_json" | jq -r '.hardwareProfile.vmSize // "N/A"')
    local os_type=$(echo "$vm_json" | jq -r '.storageProfile.osDisk.osType // "N/A"')
    local os_disk_size=$(echo "$vm_json" | jq -r '.storageProfile.osDisk.diskSizeGb // "N/A"')
    local vm_id=$(echo "$vm_json" | jq -r '.vmId // "N/A"')
    local provisioning_state=$(echo "$vm_json" | jq -r '.provisioningState // "N/A"')

    # Get VM power state
    local power_state=$(az vm get-instance-view --resource-group "$RESOURCE_GROUP_NAME" --name "$VM_NAME" --query "instanceView.statuses[?code=='PowerState/*'].displayStatus" -o tsv 2>/dev/null || echo "N/A")

    # Get OS information
    local os_publisher=$(echo "$vm_json" | jq -r '.storageProfile.imageReference.publisher // "N/A"')
    local os_offer=$(echo "$vm_json" | jq -r '.storageProfile.imageReference.offer // "N/A"')
    local os_sku=$(echo "$vm_json" | jq -r '.storageProfile.imageReference.sku // "N/A"')
    local os_version=$(echo "$vm_json" | jq -r '.storageProfile.imageReference.version // "N/A"')

    # Display VM information
    echo ""
    echo "=================================================="
    echo "           VIRTUAL MACHINE DETAILS"
    echo "=================================================="
    printf "%-25s: %s\n" "VM Name" "$VM_NAME"
    printf "%-25s: %s\n" "Power State" "$power_state"
    printf "%-25s: %s\n" "Provisioning State" "$provisioning_state"
    printf "%-25s: %s\n" "Location" "$vm_location"
    printf "%-25s: %s\n" "VM Size" "$vm_size"
    printf "%-25s: %s\n" "VM ID" "$vm_id"

    echo ""
    echo "OPERATING SYSTEM:"
    printf "%-25s: %s\n" "OS Type" "$os_type"
    printf "%-25s: %s\n" "Publisher" "$os_publisher"
    printf "%-25s: %s\n" "Offer" "$os_offer"
    printf "%-25s: %s\n" "SKU" "$os_sku"
    printf "%-25s: %s\n" "Version" "$os_version"

    echo ""
    echo "STORAGE:"
    printf "%-25s: %s GB\n" "OS Disk Size" "$os_disk_size"

    # Get data disks count
    local data_disks_count=$(echo "$vm_json" | jq '.storageProfile.dataDisks | length' 2>/dev/null || echo "0")
    printf "%-25s: %s\n" "Data Disks Count" "$data_disks_count"

    echo "=================================================="
}

# Function to get VM monitoring metrics
get_vm_monitoring_metrics() {
    log_info "Starting VM monitoring metrics collection..."

    local time_period_display=""
    case "$METRICS_TIME_PERIOD" in
        "1h") time_period_display="last 1 hour" ;;
        "6h") time_period_display="last 6 hours" ;;
        "12h") time_period_display="last 12 hours" ;;
        "1d") time_period_display="last 1 day" ;;
        "3d") time_period_display="last 3 days" ;;
        "7d") time_period_display="last 7 days" ;;
        "30d") time_period_display="last 30 days" ;;
        *) time_period_display="last 1 hour" ;;
    esac

    log_info "Fetching monitoring metrics ($time_period_display)..."

    # Get current time and calculate start time based on configured period
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")

    log_info "Time range: $hours_ago hours ago to now"

    # Calculate start time (compatible with both GNU date and BSD date)
    local start_time
    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        # GNU date (Linux)
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
        log_info "Using GNU date format"
    else
        # BSD date (macOS)
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
        log_info "Using BSD date format"
    fi

    log_info "Start time: $start_time"
    log_info "End time: $end_time"

    # Get resource ID for metrics
    log_info "Getting resource ID for metrics..."
    local resource_id=$(az vm show --resource-group "$RESOURCE_GROUP_NAME" --name "$VM_NAME" --query "id" -o tsv)

    if [[ -z "$resource_id" ]]; then
        log_error "Failed to get resource ID for metrics"
        return 1
    fi

    log_info "Resource ID: $resource_id"

    echo ""
    echo "=================================================="
    echo "           VM MONITORING METRICS ($(echo "$time_period_display" | tr '[:lower:]' '[:upper:]'))"
    echo "=================================================="

    # CPU Utilization
    log_info "Fetching CPU utilization..."
    local cpu_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "Percentage CPU" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$cpu_metrics" && "$cpu_metrics" != "null" ]]; then
        echo "$cpu_metrics" > "${VM_NAME}_cpu_metrics.json"
        local avg_cpu=$(echo "$cpu_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        printf "%-25s: %s%%\n" "Average CPU Usage" "$avg_cpu"
    else
        printf "%-25s: %s\n" "Average CPU Usage" "No data available"
    fi

    # Memory Utilization (Available Memory Bytes)
    log_info "Fetching memory metrics..."
    local memory_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "Available Memory Bytes" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$memory_metrics" && "$memory_metrics" != "null" ]]; then
        echo "$memory_metrics" > "${VM_NAME}_memory_metrics.json"
        local avg_memory_bytes=$(echo "$memory_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
        local avg_memory_gb=$(echo "$avg_memory_bytes" | awk '{if($1!="N/A") printf "%.2f GB", $1/1024/1024/1024; else print "N/A"}')
        printf "%-25s: %s\n" "Average Available Memory" "$avg_memory_gb"
    else
        printf "%-25s: %s\n" "Average Available Memory" "No data available"
    fi

    # Disk Read Operations/Sec
    log_info "Fetching disk read operations..."
    local disk_read_ops_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "Disk Read Operations/Sec" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$disk_read_ops_metrics" && "$disk_read_ops_metrics" != "null" ]]; then
        echo "$disk_read_ops_metrics" > "${VM_NAME}_disk_read_ops_metrics.json"
        local avg_disk_read_ops=$(echo "$disk_read_ops_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        printf "%-25s: %s ops/sec\n" "Average Disk Read Ops" "$avg_disk_read_ops"
    else
        printf "%-25s: %s\n" "Average Disk Read Ops" "No data available"
    fi

    # Disk Write Operations/Sec
    log_info "Fetching disk write operations..."
    local disk_write_ops_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "Disk Write Operations/Sec" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --aggregation Average \
        --output json 2>/dev/null)

    if [[ -n "$disk_write_ops_metrics" && "$disk_write_ops_metrics" != "null" ]]; then
        echo "$disk_write_ops_metrics" > "${VM_NAME}_disk_write_ops_metrics.json"
        local avg_disk_write_ops=$(echo "$disk_write_ops_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        printf "%-25s: %s ops/sec\n" "Average Disk Write Ops" "$avg_disk_write_ops"
    else
        printf "%-25s: %s\n" "Average Disk Write Ops" "No data available"
    fi

    # Network In Total
    log_info "Fetching network metrics..."
    local network_in_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "Network In Total" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --aggregation Total \
        --output json 2>/dev/null)

    if [[ -n "$network_in_metrics" && "$network_in_metrics" != "null" ]]; then
        echo "$network_in_metrics" > "${VM_NAME}_network_in_metrics.json"
        local total_network_in=$(echo "$network_in_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' | awk '{sum+=$1} END {printf "%.0f", sum}')
        local network_in_mb=$(echo "$total_network_in" | awk '{printf "%.2f MB", $1/1024/1024}')
        printf "%-25s: %s\n" "Total Network In" "$network_in_mb"
    else
        printf "%-25s: %s\n" "Total Network In" "No data available"
    fi

    # Network Out Total
    local network_out_metrics=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "Network Out Total" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --aggregation Total \
        --output json 2>/dev/null)

    if [[ -n "$network_out_metrics" && "$network_out_metrics" != "null" ]]; then
        echo "$network_out_metrics" > "${VM_NAME}_network_out_metrics.json"
        local total_network_out=$(echo "$network_out_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' | awk '{sum+=$1} END {printf "%.0f", sum}')
        local network_out_mb=$(echo "$total_network_out" | awk '{printf "%.2f MB", $1/1024/1024}')
        printf "%-25s: %s\n" "Total Network Out" "$network_out_mb"
    else
        printf "%-25s: %s\n" "Total Network Out" "No data available"
    fi

    echo "=================================================="
    log_success "All monitoring metrics saved to individual JSON files"
}

# Function to collect any metric
collect_metric() {
    local metric_name="$1"
    local resource_id="$2"
    local start_time="$3"
    local end_time="$4"

    # Determine aggregation type based on metric name
    local aggregation="Average"
    if [[ "$metric_name" == *"Total"* || "$metric_name" == *"Bytes"* ]]; then
        aggregation="Total"
    fi

    # Try to collect the metric
    az monitor metrics list \
        --resource "$resource_id" \
        --metric "$metric_name" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval "PT5M" \
        --aggregation "$aggregation" \
        --output json > "${VM_NAME}_$(echo "$metric_name" | tr ' /' '_')_metrics.json" 2>/dev/null

    # Check if file was created and has content
    if [[ -f "${VM_NAME}_$(echo "$metric_name" | tr ' /' '_')_metrics.json" && -s "${VM_NAME}_$(echo "$metric_name" | tr ' /' '_')_metrics.json" ]]; then
        return 0
    else
        rm -f "${VM_NAME}_$(echo "$metric_name" | tr ' /' '_')_metrics.json" 2>/dev/null
        return 1
    fi
}

# New efficient metrics collection function
get_vm_monitoring_metrics_new() {
    log_info "Starting VM monitoring metrics collection..."

    # Get resource ID for metrics
    log_info "Getting resource ID for metrics..."
    local resource_id=$(az vm show --resource-group "$RESOURCE_GROUP_NAME" --name "$VM_NAME" --query "id" -o tsv)

    if [[ -z "$resource_id" ]]; then
        log_error "Failed to get resource ID for metrics"
        return 1
    fi

    log_info "Resource ID: $resource_id"

    # Calculate time range
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local start_time

    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
    else
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
    fi

    log_info "Time range: $start_time to $end_time"

    # Collect metrics from METRICS_LIST
    IFS=',' read -ra METRICS_ARRAY <<< "$METRICS_LIST"
    local metrics_collected=0
    local metrics_failed=0

    for metric_name in "${METRICS_ARRAY[@]}"; do
        metric_name=$(echo "$metric_name" | xargs)  # Trim whitespace
        [[ -z "$metric_name" ]] && continue

        log_info "Collecting metric: $metric_name"
        if collect_metric "$metric_name" "$resource_id" "$start_time" "$end_time"; then
            log_success "✓ Collected: $metric_name"
            ((metrics_collected++))
        else
            log_warning "⚠ Failed: $metric_name"
            ((metrics_failed++))
        fi
    done

    log_success "Metrics collection completed: $metrics_collected collected, $metrics_failed failed"
}

# Function to get VM extensions
get_vm_extensions() {
    if [[ "$FETCH_EXTENSIONS" != "true" ]]; then
        log_info "Extension fetching is disabled (FETCH_EXTENSIONS=false)"
        return 0
    fi

    log_info "Fetching VM extensions..."

    # Get VM extensions
    local extensions_json=$(az vm extension list \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --vm-name "$VM_NAME" \
        --output json 2>/dev/null)

    if [[ -n "$extensions_json" && "$extensions_json" != "null" && "$extensions_json" != "[]" ]]; then
        echo "$extensions_json" > "${VM_NAME}_extensions.json"
        log_success "VM extensions saved to: ${VM_NAME}_extensions.json"

        # Count extensions
        local ext_count=$(echo "$extensions_json" | jq '. | length' 2>/dev/null || echo "0")
        log_info "Total extensions found: $ext_count"

        # Show extension summary
        echo ""
        echo "=================================================="
        echo "           VM EXTENSIONS"
        echo "=================================================="

        if [[ "$ext_count" -gt 0 ]]; then
            echo "$extensions_json" | jq -r '.[] | "Extension: \(.name) | Publisher: \(.publisher) | Version: \(.typeHandlerVersion) | State: \(.provisioningState)"'
        else
            echo "No extensions installed"
        fi
        echo "=================================================="
    else
        log_info "No VM extensions found"
        # Create empty extensions file for consistency
        echo "[]" > "${VM_NAME}_extensions.json"
    fi
}

# Function to get disk information
get_disk_information() {
    if [[ "$FETCH_DISKS" != "true" ]]; then
        log_info "Disk information fetching is disabled (FETCH_DISKS=false)"
        return 0
    fi

    log_info "Fetching disk information..."

    # Get all disks attached to the VM
    local vm_disks=$(az vm show --resource-group "$RESOURCE_GROUP_NAME" --name "$VM_NAME" \
        --query "{osDisk: storageProfile.osDisk, dataDisks: storageProfile.dataDisks}" \
        --output json 2>/dev/null)

    if [[ -n "$vm_disks" && "$vm_disks" != "null" ]]; then
        echo "$vm_disks" > "${VM_NAME}_disk_info.json"
        log_success "Disk information saved to: ${VM_NAME}_disk_info.json"

        # Get detailed disk information
        local os_disk_name=$(echo "$vm_disks" | jq -r '.osDisk.name // "N/A"')
        local data_disk_count=$(echo "$vm_disks" | jq '.dataDisks | length' 2>/dev/null || echo "0")

        echo ""
        echo "=================================================="
        echo "           DISK INFORMATION"
        echo "=================================================="
        printf "%-25s: %s\n" "OS Disk Name" "$os_disk_name"
        printf "%-25s: %s\n" "Data Disks Count" "$data_disk_count"

        # Get individual disk details
        if [[ "$os_disk_name" != "N/A" ]]; then
            local disk_details=$(az disk show --resource-group "$RESOURCE_GROUP_NAME" --name "$os_disk_name" --output json 2>/dev/null)
            if [[ -n "$disk_details" ]]; then
                echo "$disk_details" > "${VM_NAME}_os_disk_details.json"
                local disk_size=$(echo "$disk_details" | jq -r '.diskSizeGb // "N/A"')
                local disk_tier=$(echo "$disk_details" | jq -r '.sku.tier // "N/A"')
                local disk_type=$(echo "$disk_details" | jq -r '.sku.name // "N/A"')
                printf "%-25s: %s GB\n" "OS Disk Size" "$disk_size"
                printf "%-25s: %s\n" "OS Disk Type" "$disk_type"
                printf "%-25s: %s\n" "OS Disk Tier" "$disk_tier"
            fi
        fi

        # List data disks if any
        if [[ "$data_disk_count" -gt 0 ]]; then
            echo ""
            echo "DATA DISKS:"
            echo "$vm_disks" | jq -r '.dataDisks[] | "  Disk: \(.name) | Size: \(.diskSizeGb)GB | LUN: \(.lun)"'
        fi
        echo "=================================================="
    else
        log_warning "Failed to get disk information"
    fi
}

# Function to get network information
get_network_information() {
    if [[ "$FETCH_NETWORK" != "true" ]]; then
        log_info "Network information fetching is disabled (FETCH_NETWORK=false)"
        return 0
    fi

    log_info "Fetching network information..."

    # Get network interfaces
    local vm_nics=$(az vm nic list --resource-group "$RESOURCE_GROUP_NAME" --vm-name "$VM_NAME" --output json 2>/dev/null)

    if [[ -n "$vm_nics" && "$vm_nics" != "null" && "$vm_nics" != "[]" ]]; then
        echo "$vm_nics" > "${VM_NAME}_network_interfaces.json"
        log_success "Network interfaces saved to: ${VM_NAME}_network_interfaces.json"

        echo ""
        echo "=================================================="
        echo "           NETWORK INFORMATION"
        echo "=================================================="

        # Process each NIC
        local nic_count=$(echo "$vm_nics" | jq '. | length' 2>/dev/null || echo "0")
        printf "%-25s: %s\n" "Network Interfaces" "$nic_count"

        if [[ "$nic_count" -gt 0 ]]; then
            echo ""
            echo "NETWORK INTERFACE DETAILS:"
            echo "$vm_nics" | jq -r '.[] | "  NIC: \(.name) | Primary: \(.primary) | Private IP: \(.ipConfigurations[0].privateIpAddress // "N/A")"'

            # Get public IP information if available
            local public_ips=$(echo "$vm_nics" | jq -r '.[] | select(.ipConfigurations[0].publicIpAddress != null) | .ipConfigurations[0].publicIpAddress.id' 2>/dev/null)

            if [[ -n "$public_ips" ]]; then
                echo ""
                echo "PUBLIC IP ADDRESSES:"
                while IFS= read -r public_ip_id; do
                    if [[ -n "$public_ip_id" ]]; then
                        local public_ip_name=$(basename "$public_ip_id")
                        local public_ip_rg=$(echo "$public_ip_id" | cut -d'/' -f5)
                        local public_ip_address=$(az network public-ip show --resource-group "$public_ip_rg" --name "$public_ip_name" --query "ipAddress" -o tsv 2>/dev/null || echo "N/A")
                        echo "  Public IP: $public_ip_address ($public_ip_name)"
                    fi
                done <<< "$public_ips"
            fi
        fi
        echo "=================================================="
    else
        log_warning "Failed to get network interface information"
    fi
}

# Function to organize JSON files into service directory
organize_json_files() {
    local service_dir="azure-vm-${VM_NAME}"
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local timestamped_dir="${service_dir}_${timestamp}"

    log_info "Organizing JSON files into directory: $timestamped_dir"

    # Create service directory
    if mkdir -p "$timestamped_dir"; then
        log_success "Created directory: $timestamped_dir"
    else
        log_error "Failed to create directory: $timestamped_dir"
        return 1
    fi

    # Create subdirectories
    local vm_info_dir="$timestamped_dir/vm-info"
    local metrics_dir="$timestamped_dir/metrics"
    local network_dir="$timestamped_dir/network"
    local storage_dir="$timestamped_dir/storage"

    mkdir -p "$vm_info_dir" "$metrics_dir" "$network_dir" "$storage_dir"
    log_success "Created subdirectories: vm-info, metrics, network, storage"

    # Move files to appropriate directories
    log_info "Discovering and moving created JSON files..."

    local files_moved=0

    # Find all JSON files that match the VM name pattern
    local json_files_found=()
    while IFS= read -r -d '' file; do
        local filename=$(basename "$file")
        json_files_found+=("$filename")
    done < <(find . -maxdepth 1 -name "${VM_NAME}_*.json" -print0 2>/dev/null)

    log_info "Found ${#json_files_found[@]} JSON files to organize"

    # Organize files by type (temporarily disable errexit for file operations)
    set +e  # Disable exit on error temporarily
    for json_file in "${json_files_found[@]}"; do
        if [[ -f "$json_file" ]]; then
            local target_dir=""

            # Determine target directory based on file type
            if [[ "$json_file" == *"_full_details.json" || "$json_file" == *"_extensions.json" ]]; then
                target_dir="$vm_info_dir"
            elif [[ "$json_file" == *"_cpu_metrics.json" || "$json_file" == *"_memory_metrics.json" || "$json_file" == *"_disk_read_ops_metrics.json" || "$json_file" == *"_disk_write_ops_metrics.json" ]]; then
                target_dir="$metrics_dir"
            elif [[ "$json_file" == *"_network_"* ]]; then
                target_dir="$network_dir"
            elif [[ "$json_file" == *"_disk_"* ]]; then
                target_dir="$storage_dir"
            else
                target_dir="$timestamped_dir"  # Default to main directory
            fi

            if mv "$json_file" "$target_dir/" 2>/dev/null; then
                log_success "Moved file: $json_file -> $target_dir/"
                ((files_moved++))
            else
                log_error "Failed to move file: $json_file"
                # Continue with other files instead of exiting
            fi
        fi
    done
    set -e  # Re-enable exit on error

    # Create comprehensive summary
    create_summary "$timestamped_dir"

    log_success "Organized $files_moved files into $timestamped_dir"

    # Display directory contents
    echo ""
    echo "=================================================="
    echo "           ORGANIZED FILES"
    echo "=================================================="
    ls -la "$timestamped_dir"
    echo "=================================================="

    # Set global variable for directory name (to avoid output capture issues)
    OUTPUT_DIRECTORY="$timestamped_dir"

    return 0  # Ensure function returns success
}

# Function to create comprehensive summary
create_summary() {
    local output_dir="$1"
    log_info "Creating comprehensive summary..."

    local summary_file="$output_dir/summary.json"

    # Get current timestamp
    local collection_timestamp
    collection_timestamp=$(date -u +"%Y-%m-%d %H:%M:%S UTC")

    # Extract key information from the VM details JSON
    local vm_json="$output_dir/vm-info/${VM_NAME}_full_details.json"

    if [[ ! -f "$vm_json" ]]; then
        log_error "VM details JSON not found for summary"
        return 1
    fi

    # Extract metrics averages
    local avg_cpu="N/A"
    local avg_memory="N/A"
    local avg_disk_read="N/A"
    local avg_disk_write="N/A"
    local total_network_in="N/A"
    local total_network_out="N/A"

    # Extract CPU metrics if available
    if [[ -f "$output_dir/metrics/${VM_NAME}_cpu_metrics.json" ]]; then
        avg_cpu=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$output_dir/metrics/${VM_NAME}_cpu_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    # Extract memory metrics if available
    if [[ -f "$output_dir/metrics/${VM_NAME}_memory_metrics.json" ]]; then
        local memory_bytes=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$output_dir/metrics/${VM_NAME}_memory_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
        avg_memory=$(echo "$memory_bytes" | awk '{if($1!="N/A") printf "%.2f GB", $1/1024/1024/1024; else print "N/A"}')
    fi

    # Extract disk metrics if available
    if [[ -f "$output_dir/metrics/${VM_NAME}_disk_read_ops_metrics.json" ]]; then
        avg_disk_read=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$output_dir/metrics/${VM_NAME}_disk_read_ops_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$output_dir/metrics/${VM_NAME}_disk_write_ops_metrics.json" ]]; then
        avg_disk_write=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$output_dir/metrics/${VM_NAME}_disk_write_ops_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    # Extract network metrics if available
    if [[ -f "$output_dir/network/${VM_NAME}_network_in_metrics.json" ]]; then
        local network_in_bytes=$(jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' "$output_dir/network/${VM_NAME}_network_in_metrics.json" 2>/dev/null | awk '{sum+=$1} END {printf "%.0f", sum}')
        total_network_in=$(echo "$network_in_bytes" | awk '{printf "%.2f MB", $1/1024/1024}')
    fi

    if [[ -f "$output_dir/network/${VM_NAME}_network_out_metrics.json" ]]; then
        local network_out_bytes=$(jq -r '.value[0].timeseries[0].data[] | select(.total != null) | .total' "$output_dir/network/${VM_NAME}_network_out_metrics.json" 2>/dev/null | awk '{sum+=$1} END {printf "%.0f", sum}')
        total_network_out=$(echo "$network_out_bytes" | awk '{printf "%.2f MB", $1/1024/1024}')
    fi

    # Create comprehensive summary JSON
    cat > "$summary_file" << EOF
{
  "report_metadata": {
    "timestamp": "$collection_timestamp",
    "subscription_id": "$AZURE_SUBSCRIPTION_ID",
    "resource_group": "$RESOURCE_GROUP_NAME",
    "vm_name": "$VM_NAME",
    "metrics_time_period": "$METRICS_TIME_PERIOD",
    "service_type": "Azure Virtual Machine"
  },
  "vm_summary": $(jq '{
    name: .name,
    location: .location,
    vm_size: .hardwareProfile.vmSize,
    os_type: .storageProfile.osDisk.osType,
    provisioning_state: .provisioningState,
    vm_id: .vmId
  }' "$vm_json"),
  "performance_metrics": {
    "avg_cpu_percent": "$avg_cpu",
    "avg_available_memory": "$avg_memory",
    "avg_disk_read_ops_per_sec": "$avg_disk_read",
    "avg_disk_write_ops_per_sec": "$avg_disk_write",
    "total_network_in": "$total_network_in",
    "total_network_out": "$total_network_out"
  },
  "data_files": {
    "vm_details": "vm-info/${VM_NAME}_full_details.json",
    "extensions": "vm-info/${VM_NAME}_extensions.json",
    "cpu_metrics": "metrics/${VM_NAME}_cpu_metrics.json",
    "memory_metrics": "metrics/${VM_NAME}_memory_metrics.json",
    "disk_read_metrics": "metrics/${VM_NAME}_disk_read_ops_metrics.json",
    "disk_write_metrics": "metrics/${VM_NAME}_disk_write_ops_metrics.json",
    "network_in_metrics": "network/${VM_NAME}_network_in_metrics.json",
    "network_out_metrics": "network/${VM_NAME}_network_out_metrics.json",
    "network_interfaces": "network/${VM_NAME}_network_interfaces.json",
    "disk_info": "storage/${VM_NAME}_disk_info.json",
    "os_disk_details": "storage/${VM_NAME}_os_disk_details.json"
  },
  "collection_info": {
    "total_files_collected": $(find "$output_dir" -name "*.json" | wc -l),
    "metrics_time_range": "$METRICS_TIME_PERIOD",
    "extensions_collected": $(if [[ -f "$output_dir/vm-info/${VM_NAME}_extensions.json" ]]; then jq '. | length' "$output_dir/vm-info/${VM_NAME}_extensions.json" 2>/dev/null || echo "0"; else echo "0"; fi),
    "note": "All metrics and configuration data organized by category"
  }
}
EOF

    log_success "Summary JSON created with key metrics and file references"
}

# Function to create ZIP file if requested
create_zip_file() {
    if [[ "$GENERATE_ZIP" != "true" ]]; then
        log_info "ZIP creation disabled (GENERATE_ZIP=false)"
        return 0
    fi

    if [[ -z "$OUTPUT_DIRECTORY" || ! -d "$OUTPUT_DIRECTORY" ]]; then
        log_error "Output directory not found for ZIP creation: $OUTPUT_DIRECTORY"
        return 1
    fi

    log_info "Creating ZIP archive for directory: $OUTPUT_DIRECTORY"

    local zip_file="${OUTPUT_DIRECTORY}.zip"

    # Remove existing ZIP file if it exists
    if [[ -f "$zip_file" ]]; then
        log_info "Removing existing ZIP file: $zip_file"
        rm -f "$zip_file"
    fi

    if command -v zip &> /dev/null; then
        log_info "Using zip utility to create archive..."

        # Create ZIP file with verbose output for debugging
        if zip -r "$zip_file" "$OUTPUT_DIRECTORY" 2>&1; then
            if [[ -f "$zip_file" ]]; then
                log_success "ZIP file created successfully: $zip_file"

                # Get ZIP file size
                local zip_size
                if command -v du &> /dev/null; then
                    zip_size=$(du -h "$zip_file" 2>/dev/null | cut -f1)
                    log_info "ZIP file size: $zip_size"
                fi

                # Get file count in ZIP
                local file_count
                if command -v unzip &> /dev/null; then
                    file_count=$(unzip -l "$zip_file" 2>/dev/null | tail -1 | awk '{print $2}')
                    log_info "Files in ZIP: $file_count"
                fi

                # Remove original directory after successful ZIP creation
                log_info "Removing original directory: $OUTPUT_DIRECTORY"
                if rm -rf "$OUTPUT_DIRECTORY"; then
                    log_success "Original directory removed successfully"
                    log_info "Final output: $zip_file"
                else
                    log_error "Failed to remove original directory: $OUTPUT_DIRECTORY"
                    log_warning "ZIP file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                log_error "ZIP file was not created successfully"
                return 1
            fi
        else
            log_error "Failed to create ZIP file using zip utility"
            return 1
        fi
    elif command -v tar &> /dev/null; then
        log_info "ZIP utility not available, using tar with gzip compression..."

        local tar_file="${directory}.tar.gz"

        # Remove existing tar file if it exists
        if [[ -f "$tar_file" ]]; then
            log_info "Removing existing TAR.GZ file: $tar_file"
            rm -f "$tar_file"
        fi

        if tar -czf "$tar_file" "$directory" 2>&1; then
            if [[ -f "$tar_file" ]]; then
                log_success "TAR.GZ file created successfully: $tar_file"

                # Get file size
                local tar_size
                if command -v du &> /dev/null; then
                    tar_size=$(du -h "$tar_file" 2>/dev/null | cut -f1)
                    log_info "TAR.GZ file size: $tar_size"
                fi

                # Remove original directory after successful TAR creation
                log_info "Removing original directory: $directory"
                if rm -rf "$directory"; then
                    log_success "Original directory removed successfully"
                    log_info "Final output: $tar_file"
                else
                    log_error "Failed to remove original directory: $directory"
                    log_warning "TAR.GZ file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                log_error "TAR.GZ file was not created successfully"
                return 1
            fi
        else
            log_error "Failed to create TAR.GZ file"
            return 1
        fi
    else
        log_warning "Neither zip nor tar utilities are available, skipping archive creation"
        log_info "Please install zip or tar to enable archive creation"
        return 1
    fi
}

# Function to show help
show_help() {
    echo ""
    echo "Azure Virtual Machine Monitoring Script"
    echo "======================================="
    echo ""
    echo "USAGE:"
    echo "  $0 --config-path <config-file> [--generate-zip <true|false>]"
    echo ""
    echo "REQUIRED ARGUMENTS:"
    echo "  --config-path <file>    Path to configuration file (.json or .conf)"
    echo ""
    echo "OPTIONAL ARGUMENTS:"
    echo "  --generate-zip <bool>   Create ZIP archive (default: true)"
    echo "  --help, -h             Show this help message"
    echo ""
    echo "EXAMPLES:"
    echo "  # JSON configuration (recommended) - ZIP generated by default"
    echo "  $0 --config-path config.json"
    echo ""
    echo "  # Legacy .conf configuration - ZIP generated by default"
    echo "  $0 --config-path config.conf"
    echo ""
    echo "  # Disable ZIP generation (keep directory structure)"
    echo "  $0 --config-path config.json --generate-zip false"
    echo ""
    echo "JSON CONFIGURATION FORMAT:"
    echo "  {"
    echo "    \"azure\": {"
    echo "      \"subscription_id\": \"your-subscription-id\","
    echo "      \"resource_group\": \"your-resource-group\","
    echo "      \"vm_name\": \"your-vm-name\""
    echo "    },"
    echo "    \"monitoring\": {"
    echo "      \"metrics_time_period\": \"1h\""
    echo "    },"
    echo "    \"collection\": {"
    echo "      \"collect_metrics\": true,"
    echo "      \"metrics_list\": [\"Percentage CPU\", \"Available Memory Bytes\", \"Network In Total\"]"
    echo "    }"
    echo "  }"
    echo ""
    echo "LEGACY .CONF CONFIGURATION FORMAT:"
    echo "  AZURE_SUBSCRIPTION_ID=\"your-subscription-id\""
    echo "  RESOURCE_GROUP_NAME=\"your-resource-group\""
    echo "  VM_NAME=\"your-vm-name\""
    echo "  METRICS_TIME_PERIOD=\"1h\"  # Options: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
    echo "  COLLECT_METRICS=\"true\"    # true/false"
    echo "  FETCH_EXTENSIONS=\"true\"   # true/false"
    echo "  FETCH_DISKS=\"true\"        # true/false"
    echo "  FETCH_NETWORK=\"true\"      # true/false"
    echo ""
    echo "FEATURES:"
    echo "  • JSON configuration support with auto-detection"
    echo "  • Dynamic metrics collection with configurable lists"
    echo "  • Comprehensive VM monitoring with UTC timestamps"
    echo "  • CPU, memory, disk, and network metrics collection"
    echo "  • VM extensions and disk information"
    echo "  • Network interface and public IP details"
    echo "  • Organized output with summary reports"
    echo "  • ZIP archive support for easy sharing"
    echo ""
}

# Main function
main() {
    local config_file="$1"

    log_info "Starting Azure Virtual Machine monitoring script"
    log_info "=================================================="

    # Check if config file is provided
    if [[ -z "$config_file" ]]; then
        log_error "No configuration file provided"
        exit 1
    fi

    log_info "Generate ZIP: $GENERATE_ZIP"

    # Step 1: Check prerequisites
    check_prerequisites

    # Step 2: Validate and load configuration
    validate_config "$config_file"

    # Step 3: Check Azure login and subscription
    check_azure_login

    # Step 4: Validate resource group
    validate_resource_group

    # Step 5: Check if VM exists
    check_vm_exists

    # Step 6: Get detailed VM information
    get_detailed_vm_info

    # Step 7: Get monitoring metrics (if enabled)
    if [[ "$COLLECT_METRICS" == "true" ]]; then
        get_vm_monitoring_metrics_new
    else
        log_info "Skipping metrics collection (COLLECT_METRICS=false)"
    fi

    # Step 8: Get VM extensions (if enabled)
    get_vm_extensions

    # Step 9: Get disk information (if enabled)
    get_disk_information

    # Step 10: Get network information (if enabled)
    get_network_information

    # Step 11: Organize files
    if organize_json_files; then
        log_success "File organization completed successfully"
    else
        log_warning "File organization had issues, but continuing to ZIP creation"
    fi

    # Step 12: Create ZIP file if requested
    create_zip_file

    log_success "Azure VM monitoring completed successfully!"
    log_info "=================================================="
}

# Argument parsing
if [[ $# -eq 0 ]]; then
    log_error "Missing required arguments"
    show_help
    exit 1
fi

# Initialize variables
CONFIG_PATH=""
GENERATE_ZIP="true"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --config-path)
            CONFIG_PATH="$2"
            shift 2
            ;;
        --generate-zip)
            GENERATE_ZIP="$2"
            shift 2
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown argument: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required arguments
if [[ -z "$CONFIG_PATH" ]]; then
    log_error "Missing required argument: --config-path"
    show_help
    exit 1
fi

if [[ -z "$GENERATE_ZIP" ]]; then
    log_error "Missing required argument: --generate-zip"
    show_help
    exit 1
fi

# Validate config file exists
if [[ ! -f "$CONFIG_PATH" ]]; then
    log_error "Configuration file not found: $CONFIG_PATH"
    exit 1
fi

# Validate generate-zip argument (default to true if not specified)
if [[ -n "$GENERATE_ZIP" && "$GENERATE_ZIP" != "true" && "$GENERATE_ZIP" != "false" ]]; then
    log_error "Invalid value for --generate-zip: $GENERATE_ZIP"
    log_info "Valid values: true, false"
    exit 1
fi

# Set default value if not specified
GENERATE_ZIP="${GENERATE_ZIP:-true}"

# Execute main function
main "$CONFIG_PATH"
