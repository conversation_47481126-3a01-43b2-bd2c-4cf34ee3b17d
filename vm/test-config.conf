# Azure Virtual Machine Monitoring Configuration
# ==============================================

# Azure Configuration (Required)
AZURE_SUBSCRIPTION_ID="a78d329d-3056-4e59-a5e9-0cf78f204774"
RESOURCE_GROUP_NAME="dev-byoa-shared-2-rg"
VM_NAME="clonrerefresh001-rqxidlx-server1_0"

# Monitoring Configuration
METRICS_TIME_PERIOD="30d"  # Options: 1h, 6h, 12h, 1d, 3d, 7d, 30d

# Feature Configuration (Optional - defaults to true)
FETCH_EXTENSIONS="true"   # Set to "false" to skip VM extensions collection
FETCH_DISKS="true"        # Set to "false" to skip disk information collection
FETCH_NETWORK="true"      # Set to "false" to skip network information collection

# Notes:
# - AZURE_SUBSCRIPTION_ID: Your Azure subscription ID
# - RESOURCE_GROUP_NAME: Resource group containing the VM
# - VM_NAME: Name of the Virtual Machine to monitor
# - METRICS_TIME_PERIOD: Time period for metrics collection
# - FETCH_* options: Enable/disable specific data collection features
