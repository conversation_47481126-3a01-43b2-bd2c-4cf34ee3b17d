#!/bin/bash

# Generic Bash Logger Library
# Version: 1.0
# Description: Centralized logging functionality for any bash scripts
# Usage: source shared/bash-logger.sh

# Prevent multiple sourcing
if [[ "${BASH_LOGGER_LOADED:-}" == "true" ]]; then
    return 0
fi
export BASH_LOGGER_LOADED="true"

# Logger configuration
export LOGGER_VERSION="1.0"
export LOGGER_NAME="Bash-Logger"

# Color codes for output
readonly LOG_COLOR_RED='\033[0;31m'
readonly LOG_COLOR_GREEN='\033[0;32m'
readonly LOG_COLOR_YELLOW='\033[1;33m'
readonly LOG_COLOR_BLUE='\033[0;34m'
readonly LOG_COLOR_PURPLE='\033[0;35m'
readonly LOG_COLOR_CYAN='\033[0;36m'
readonly LOG_COLOR_WHITE='\033[1;37m'
readonly LOG_COLOR_GRAY='\033[0;90m'
readonly LOG_COLOR_NC='\033[0m' # No Color

# Log levels
readonly LOG_LEVEL_TRACE=0
readonly LOG_LEVEL_DEBUG=1
readonly LOG_LEVEL_INFO=2
readonly LOG_LEVEL_SUCCESS=3
readonly LOG_LEVEL_WARNING=4
readonly LOG_LEVEL_ERROR=5
readonly LOG_LEVEL_CRITICAL=6

# Default configuration
export LOG_LEVEL="${LOG_LEVEL:-$LOG_LEVEL_INFO}"
export LOG_TIMESTAMP_FORMAT="${LOG_TIMESTAMP_FORMAT:-%Y-%m-%d %H:%M:%S}"
export LOG_ENABLE_COLORS="${LOG_ENABLE_COLORS:-true}"
export LOG_ENABLE_FILE="${LOG_ENABLE_FILE:-false}"
export LOG_FILE_PATH="${LOG_FILE_PATH:-}"
export LOG_ENABLE_SYSLOG="${LOG_ENABLE_SYSLOG:-false}"
export LOG_SCRIPT_NAME="${LOG_SCRIPT_NAME:-$(basename "${BASH_SOURCE[1]}")}"

# Internal variables
LOG_SESSION_ID="$(date +%s)-$$"
LOG_START_TIME=$(date +%s)

# Function to get timestamp (includes both local and UTC time)
get_timestamp() {
    local local_time=$(date +"$LOG_TIMESTAMP_FORMAT")
    local utc_time=$(date -u +"$LOG_TIMESTAMP_FORMAT")
    echo "${local_time} (UTC: ${utc_time})"
}

# Function to get log level name
get_log_level_name() {
    local level=$1
    case $level in
        $LOG_LEVEL_TRACE)    echo "TRACE" ;;
        $LOG_LEVEL_DEBUG)    echo "DEBUG" ;;
        $LOG_LEVEL_INFO)     echo "INFO" ;;
        $LOG_LEVEL_SUCCESS)  echo "SUCCESS" ;;
        $LOG_LEVEL_WARNING)  echo "WARNING" ;;
        $LOG_LEVEL_ERROR)    echo "ERROR" ;;
        $LOG_LEVEL_CRITICAL) echo "CRITICAL" ;;
        *)                   echo "UNKNOWN" ;;
    esac
}

# Function to get log level color
get_log_level_color() {
    local level=$1
    case $level in
        $LOG_LEVEL_TRACE)    echo "$LOG_COLOR_GRAY" ;;
        $LOG_LEVEL_DEBUG)    echo "$LOG_COLOR_PURPLE" ;;
        $LOG_LEVEL_INFO)     echo "$LOG_COLOR_BLUE" ;;
        $LOG_LEVEL_SUCCESS)  echo "$LOG_COLOR_GREEN" ;;
        $LOG_LEVEL_WARNING)  echo "$LOG_COLOR_YELLOW" ;;
        $LOG_LEVEL_ERROR)    echo "$LOG_COLOR_RED" ;;
        $LOG_LEVEL_CRITICAL) echo "$LOG_COLOR_WHITE" ;;
        *)                   echo "$LOG_COLOR_NC" ;;
    esac
}

# Core logging function
log_message() {
    local level=$1
    local message="$2"
    local component="${3:-MAIN}"
    
    # Check if message should be logged based on level
    if [[ $level -lt $LOG_LEVEL ]]; then
        return 0
    fi
    
    local timestamp=$(get_timestamp)
    local level_name=$(get_log_level_name "$level")
    local level_color=$(get_log_level_color "$level")
    
    # Format message
    local formatted_message="[$timestamp] [$LOG_SCRIPT_NAME] [$component] [$level_name] $message"
    
    # Console output with colors
    if [[ "$LOG_ENABLE_COLORS" == "true" && -t 1 ]]; then
        echo -e "${level_color}${formatted_message}${LOG_COLOR_NC}"
    else
        echo "$formatted_message"
    fi
    
    # File logging
    if [[ "$LOG_ENABLE_FILE" == "true" && -n "$LOG_FILE_PATH" ]]; then
        echo "$formatted_message" >> "$LOG_FILE_PATH"
    fi
    
    # Syslog logging
    if [[ "$LOG_ENABLE_SYSLOG" == "true" ]] && command -v logger &> /dev/null; then
        logger -t "$LOGGER_NAME" "$formatted_message"
    fi
}

# Convenience logging functions
log_trace() {
    log_message $LOG_LEVEL_TRACE "$1" "${2:-}"
}

log_debug() {
    log_message $LOG_LEVEL_DEBUG "$1" "${2:-}"
}

log_info() {
    log_message $LOG_LEVEL_INFO "$1" "${2:-}"
}

log_success() {
    log_message $LOG_LEVEL_SUCCESS "$1" "${2:-}"
}

log_warning() {
    log_message $LOG_LEVEL_WARNING "$1" "${2:-}"
}

log_error() {
    log_message $LOG_LEVEL_ERROR "$1" "${2:-}"
}

log_critical() {
    log_message $LOG_LEVEL_CRITICAL "$1" "${2:-}"
}

# Backward compatibility aliases (matching existing function names)
print_info() {
    log_info "$1" "${2:-}"
}

print_success() {
    log_success "$1" "${2:-}"
}

print_warning() {
    log_warning "$1" "${2:-}"
}

print_error() {
    log_error "$1" "${2:-}"
}

# Advanced logging functions
log_separator() {
    local char="${1:-=}"
    local length="${2:-50}"
    local separator=$(printf "%*s" "$length" | tr ' ' "$char")
    log_info "$separator"
}

log_header() {
    local title="$1"
    local char="${2:-=}"
    local length="${3:-50}"
    
    log_separator "$char" "$length"
    local padding=$(( (length - ${#title}) / 2 ))
    local padded_title=$(printf "%*s%s%*s" $padding "" "$title" $padding "")
    log_info "$padded_title"
    log_separator "$char" "$length"
}

log_section() {
    local title="$1"
    log_info ""
    log_info "$title:"
    log_separator "-" "${#title}"
}

log_key_value() {
    local key="$1"
    local value="$2"
    local width="${3:-25}"
    local formatted_message=$(printf "%-${width}s: %s" "$key" "$value")
    log_info "$formatted_message"
}

log_progress() {
    local current="$1"
    local total="$2"
    local message="${3:-Processing}"
    local percentage=$((current * 100 / total))
    log_info "$message... ($current/$total - $percentage%)"
}

log_duration() {
    local start_time="$1"
    local end_time="${2:-$(date +%s)}"
    local duration=$((end_time - start_time))
    local hours=$((duration / 3600))
    local minutes=$(((duration % 3600) / 60))
    local seconds=$((duration % 60))
    
    if [[ $hours -gt 0 ]]; then
        log_info "Duration: ${hours}h ${minutes}m ${seconds}s"
    elif [[ $minutes -gt 0 ]]; then
        log_info "Duration: ${minutes}m ${seconds}s"
    else
        log_info "Duration: ${seconds}s"
    fi
}

# Configuration functions
log_set_level() {
    local level_name="$1"
    case "${level_name^^}" in
        "TRACE")    export LOG_LEVEL=$LOG_LEVEL_TRACE ;;
        "DEBUG")    export LOG_LEVEL=$LOG_LEVEL_DEBUG ;;
        "INFO")     export LOG_LEVEL=$LOG_LEVEL_INFO ;;
        "SUCCESS")  export LOG_LEVEL=$LOG_LEVEL_SUCCESS ;;
        "WARNING")  export LOG_LEVEL=$LOG_LEVEL_WARNING ;;
        "ERROR")    export LOG_LEVEL=$LOG_LEVEL_ERROR ;;
        "CRITICAL") export LOG_LEVEL=$LOG_LEVEL_CRITICAL ;;
        *)          log_error "Invalid log level: $level_name" ;;
    esac
}

log_enable_file() {
    local file_path="$1"
    export LOG_ENABLE_FILE="true"
    export LOG_FILE_PATH="$file_path"
    
    # Create log directory if it doesn't exist
    local log_dir=$(dirname "$file_path")
    if [[ ! -d "$log_dir" ]]; then
        mkdir -p "$log_dir"
    fi
    
    # Initialize log file with session header
    {
        echo "=================================================="
        echo "Log Session Started: $(get_timestamp)"
        echo "Script: $LOG_SCRIPT_NAME"
        echo "Session ID: $LOG_SESSION_ID"
        echo "PID: $$"
        echo "User: $(whoami)"
        echo "Host: $(hostname)"
        echo "Working Directory: $(pwd)"
        echo "=================================================="
    } >> "$file_path"
    
    log_info "File logging enabled: $file_path"
}

log_disable_file() {
    export LOG_ENABLE_FILE="false"
    log_info "File logging disabled"
}

log_enable_colors() {
    export LOG_ENABLE_COLORS="true"
}

log_disable_colors() {
    export LOG_ENABLE_COLORS="false"
}

log_enable_syslog() {
    export LOG_ENABLE_SYSLOG="true"
    log_info "Syslog logging enabled"
}

log_disable_syslog() {
    export LOG_ENABLE_SYSLOG="false"
}

# Utility functions
log_script_start() {
    local script_name="${1:-$LOG_SCRIPT_NAME}"
    local version="${2:-}"
    
    log_header "SCRIPT EXECUTION START"
    log_key_value "Script Name" "$script_name"
    if [[ -n "$version" ]]; then
        log_key_value "Version" "$version"
    fi
    log_key_value "Start Time" "$(get_timestamp)"
    log_key_value "Session ID" "$LOG_SESSION_ID"
    log_key_value "Process ID" "$$"
    log_key_value "User" "$(whoami)"
    log_key_value "Host" "$(hostname)"
    log_key_value "Working Directory" "$(pwd)"
    log_separator
}

log_script_end() {
    local exit_code="${1:-0}"
    local end_time=$(date +%s)
    
    log_separator
    log_header "SCRIPT EXECUTION END"
    log_key_value "End Time" "$(get_timestamp)"
    log_key_value "Exit Code" "$exit_code"
    log_duration "$LOG_START_TIME" "$end_time"
    log_separator
    
    if [[ "$LOG_ENABLE_FILE" == "true" && -n "$LOG_FILE_PATH" ]]; then
        {
            echo "=================================================="
            echo "Log Session Ended: $(get_timestamp)"
            echo "Exit Code: $exit_code"
            echo "Session Duration: $((end_time - LOG_START_TIME)) seconds"
            echo "=================================================="
        } >> "$LOG_FILE_PATH"
    fi
}

# Error handling integration
log_trap_error() {
    local exit_code=$?
    local line_number=$1
    log_critical "Script failed at line $line_number with exit code $exit_code"
    log_script_end "$exit_code"
    exit "$exit_code"
}

log_setup_error_trap() {
    trap 'log_trap_error $LINENO' ERR
}

# Initialization message
log_debug "Generic Bash Logger v$LOGGER_VERSION loaded for script: $LOG_SCRIPT_NAME"
