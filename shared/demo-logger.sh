#!/bin/bash

# Demo script to show how the generic bash logger works

# Import the logger
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Configure logging environment
export SCRIPT_LOG_ENV="development"  # This enables debug logging and file output
export LOG_SCRIPT_NAME="Logger-Demo"

# Source the logger
source "$SCRIPT_DIR/bash-logger.sh"

# Start script logging
log_script_start "Generic Logger Demo" "1.0"

echo ""
echo "🚀 DEMONSTRATING BASH LOGGER FEATURES"
echo "=================================================="

# 1. Test all log levels
echo ""
echo "1. LOG LEVELS:"
echo "----------------------------------------"
log_trace "This is a TRACE message (level 0) - most detailed debugging"
log_debug "This is a DEBUG message (level 1) - general debugging info"
log_info "This is an INFO message (level 2) - general information"
log_success "This is a SUCCESS message (level 3) - operation completed"
log_warning "This is a WARNING message (level 4) - potential issue"
log_error "This is an ERROR message (level 5) - recoverable error"
log_critical "This is a CRITICAL message (level 6) - fatal error"

# 2. Test advanced formatting
echo ""
echo "2. ADVANCED FORMATTING:"
echo "----------------------------------------"
log_header "SYSTEM INFORMATION"

log_section "Configuration Details"
log_key_value "Script Name" "$LOG_SCRIPT_NAME"
log_key_value "Log Level" "$LOG_LEVEL"
log_key_value "Colors Enabled" "$LOG_ENABLE_COLORS"
log_key_value "File Logging" "$LOG_ENABLE_FILE"

log_section "Environment Information"
log_key_value "Hostname" "$(hostname)"
log_key_value "User" "$(whoami)"
log_key_value "Working Directory" "$(pwd)"
log_key_value "Shell" "$SHELL"

# 3. Test separators
echo ""
echo "3. SEPARATORS:"
echo "----------------------------------------"
log_separator "=" 50
log_info "This is between equal sign separators"
log_separator "=" 50

log_separator "-" 30
log_info "This is between dash separators"
log_separator "-" 30

# 4. Test progress tracking
echo ""
echo "4. PROGRESS TRACKING:"
echo "----------------------------------------"
log_info "Simulating a process with progress tracking..."

for i in {1..5}; do
    log_progress "$i" "5" "Processing items"
    sleep 0.5
done

log_success "Progress tracking completed"

# 5. Test duration tracking
echo ""
echo "5. DURATION TRACKING:"
echo "----------------------------------------"
start_time=$(date +%s)
log_info "Starting timed operation..."
sleep 2
log_duration "$start_time"

# 6. Test component logging
echo ""
echo "6. COMPONENT LOGGING:"
echo "----------------------------------------"
log_info "Default component message"
log_info "Database component message" "DATABASE"
log_info "Network component message" "NETWORK"
log_warning "Authentication warning" "AUTH"
log_error "Connection error" "NETWORK"

# 7. Test configuration display
echo ""
echo "7. LOGGER CONFIGURATION:"
echo "----------------------------------------"
show_logger_config

# 8. Test file logging (if enabled)
echo ""
echo "8. FILE LOGGING:"
echo "----------------------------------------"
if [[ "$LOG_ENABLE_FILE" == "true" ]]; then
    log_info "File logging is enabled"
    log_info "Log file: $LOG_FILE_PATH"
    
    if [[ -f "$LOG_FILE_PATH" ]]; then
        file_size=$(wc -c < "$LOG_FILE_PATH")
        log_key_value "Log file size" "${file_size} bytes"
        log_info "You can view the log file with: cat $LOG_FILE_PATH"
    fi
else
    log_warning "File logging is not enabled"
    log_info "To enable: export LOG_ENABLE_FILE=true"
fi

# 9. Test backward compatibility
echo ""
echo "9. BACKWARD COMPATIBILITY:"
echo "----------------------------------------"
log_info "Testing old function names (backward compatibility):"
print_info "This uses the old print_info function"
print_success "This uses the old print_success function"
print_warning "This uses the old print_warning function"
print_error "This uses the old print_error function"

# End script logging
log_success "Logger demonstration completed successfully!"
log_script_end 0
