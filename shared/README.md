# Generic Bash Logger Library

Enterprise-grade logging library for bash scripts with UTC timestamps, multiple output formats, and professional formatting.

## ✨ **Key Features**

- **🕐 UTC + Local Timestamps** - Dual timezone support for global teams
- **🎨 Color-Coded Output** - Professional visual formatting
- **📊 Multiple Log Levels** - TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL
- **📁 Multiple Outputs** - Console, file, and syslog support
- **🏗️ Advanced Formatting** - Headers, sections, key-value pairs, progress tracking
- **🔄 Backward Compatibility** - Works with existing `print_*` functions
- **⚙️ Environment-Based Config** - Easy configuration for different environments

## 🚀 **Quick Start**

### **1. Basic Integration**
```bash
#!/bin/bash

# Import the logger
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/bash-logger.sh"

# Configure your script
export LOG_SCRIPT_NAME="My-Script"

# Start logging
log_script_start "My Amazing Script" "2.0"

# Use logging functions
log_info "Starting script execution"
log_success "Configuration loaded successfully"
log_warning "This is just a warning"
log_error "Something went wrong"

# End logging
log_script_end 0
```

### **2. Professional Output**
```bash
[2025-07-14 13:02:43 (UTC: 2025-07-14 07:32:43)] [My-Script] [MAIN] [INFO] Starting script execution
[2025-07-14 13:02:43 (UTC: 2025-07-14 07:32:43)] [My-Script] [MAIN] [SUCCESS] Configuration loaded successfully
[2025-07-14 13:02:43 (UTC: 2025-07-14 07:32:43)] [My-Script] [MAIN] [WARNING] This is just a warning
[2025-07-14 13:02:43 (UTC: 2025-07-14 07:32:43)] [My-Script] [MAIN] [ERROR] Something went wrong
```

## 📊 **Log Levels and Colors**

| Level | Number | Color | Usage |
|-------|--------|-------|-------|
| TRACE | 0 | Gray | Most detailed debugging |
| DEBUG | 1 | Purple | General debugging |
| INFO | 2 | Blue | General information |
| SUCCESS | 3 | Green | Success messages |
| WARNING | 4 | Yellow | Warnings |
| ERROR | 5 | Red | Recoverable errors |
| CRITICAL | 6 | White | Fatal errors |

## ⚙️ **Configuration Options**

### **Environment-Based Configuration**
```bash
# Quick environment setup
export SCRIPT_LOG_ENV="development"  # Enables debug logging and file output
export SCRIPT_LOG_ENV="production"   # Production-ready logging
export SCRIPT_LOG_ENV="testing"      # Testing environment
export SCRIPT_LOG_ENV="verbose"      # Maximum logging detail
export SCRIPT_LOG_ENV="silent"       # Minimal logging
```

### **Manual Configuration**
```bash
export LOG_LEVEL=1                    # 0-6 (TRACE to CRITICAL)
export LOG_ENABLE_COLORS=true         # true/false
export LOG_ENABLE_FILE=true           # true/false
export LOG_FILE_PATH="logs/app.log"   # File path
export LOG_ENABLE_SYSLOG=true         # true/false
export LOG_SCRIPT_NAME="MyApp"        # Script identifier
```

## 🏗️ **Advanced Features**

### **Structured Formatting**
```bash
# Headers and sections
log_header "MAIN PROCESSING"          # Creates bordered header
log_section "Database Setup"          # Creates section with underline
log_separator "=" 50                  # Creates separator line

# Key-value pairs
log_key_value "Server" "localhost"    # Key: Value formatting
log_key_value "Port" "3306" 15        # Custom width

# Progress tracking
log_progress "5" "10" "Processing"    # Shows: Processing... (5/10 - 50%)

# Duration tracking
start_time=$(date +%s)
# ... work ...
log_duration "$start_time"            # Shows: Duration: 2m 30s
```

### **Component Logging**
```bash
log_info "Database connected" "DB"    # [DB] component
log_error "Network timeout" "NET"     # [NET] component
log_warning "Auth failed" "AUTH"      # [AUTH] component
```

### **Script Lifecycle**
```bash
# Start script with metadata
log_script_start "My Script" "1.0"

# Your script logic here...

# End script with exit code
log_script_end 0  # or $?
```

## 📁 **File Logging**

### **Enable File Logging**
```bash
export LOG_ENABLE_FILE=true
export LOG_FILE_PATH="logs/myapp.log"

# Or use environment preset
export SCRIPT_LOG_ENV="development"  # Automatically enables file logging
```

### **Log File Format**
```
==================================================
Log Session Started: 2025-07-14 13:02:43 (UTC: 2025-07-14 07:32:43)
Script: My-Script
Session ID: 1752236644-22783
PID: 22783
User: amitsharma
Host: macbook-pro-2.tail94b34.ts.net
==================================================
[2025-07-14 13:02:43 (UTC: 2025-07-14 07:32:43)] [My-Script] [MAIN] [INFO] Starting script execution
[2025-07-14 13:02:43 (UTC: 2025-07-14 07:32:43)] [My-Script] [MAIN] [SUCCESS] Configuration loaded
==================================================
Log Session Ended: 2025-07-14 13:02:47 (UTC: 2025-07-14 07:32:47)
Exit Code: 0
Session Duration: 4 seconds
==================================================
```

## 🔄 **Backward Compatibility**

The logger maintains compatibility with existing functions:
```bash
# Old functions still work
print_info "This still works"
print_success "Legacy compatibility"
print_warning "No code changes needed"
print_error "Seamless migration"

# New functions provide enhanced features
log_info "Enhanced with timestamps and components"
log_success "Professional enterprise logging"
```

## 🌍 **Global Team Benefits**

### **UTC Timestamp Advantages**
- **Multi-timezone coordination** - Teams worldwide can correlate logs
- **Audit compliance** - Standardized timestamps for compliance
- **Log aggregation** - Unified time reference across systems
- **Troubleshooting** - Easy correlation across distributed systems

### **Example Multi-Timezone Usage**
```bash
# Team in India sees:
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Database backup started

# Team in US sees the same UTC time for correlation:
[2025-07-14 08:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Database backup started

# Team in UK sees:
[2025-07-14 08:43:52 (UTC: 2025-07-14 07:43:52)] [INFO] Database backup started
```

## 📈 **Enterprise Integration**

Perfect for:
- **Production monitoring scripts** - Professional logging standards
- **CI/CD pipelines** - Structured output for automation
- **System administration** - Consistent logging across tools
- **Compliance auditing** - Timestamped audit trails
- **Multi-team environments** - Standardized logging format
- **Global operations** - UTC timestamp coordination

Transform your bash scripts into **enterprise-grade tools** with professional logging! 🌟
