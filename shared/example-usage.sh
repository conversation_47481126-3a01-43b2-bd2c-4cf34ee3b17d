#!/bin/bash

# Example: How to use the generic bash logger in your scripts

# Step 1: Import the logger
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/bash-logger.sh"

# Step 2: Configure your script
export LOG_SCRIPT_NAME="My-Script"

# Step 3: Start script logging
log_script_start "My Amazing Script" "2.0"

# Step 4: Use logging functions
log_info "Starting script execution"
log_success "Configuration loaded successfully"
log_warning "This is just a warning"

# Step 5: Use advanced features
log_header "PROCESSING DATA"

log_section "Database Connection"
log_key_value "Host" "localhost"
log_key_value "Port" "5432"
log_key_value "Database" "myapp"

# Step 6: Progress tracking
log_section "Data Processing"
for i in {1..3}; do
    log_progress "$i" "3" "Processing records"
    sleep 1
done

# Step 7: End script logging
log_script_end 0
