# Config-Driven Data Collection Architecture Proposal

## 🎯 **Objective**
Transform Azure monitoring scripts from processing-heavy to pure data collection tools with configurable metric collection and JSON-only output.

## 🔧 **Current Issues**
1. **Mixed Responsibilities**: Scripts both collect AND process data
2. **Hard-coded Metrics**: Metric names are embedded in code
3. **Processing Overhead**: Complex calculations and formatting in collection scripts
4. **Inflexible**: Cannot easily enable/disable specific metrics
5. **Multiple az commands**: Repetitive Azure CLI calls for similar operations

## 🚀 **Proposed Solution**

### **1. Configuration-Driven Metrics Collection**
```bash
# Enable/disable specific metrics in config file
COLLECT_CPU_METRICS="true"
COLLECT_MEMORY_METRICS="true" 
COLLECT_STORAGE_METRICS="false"  # User can disable unwanted metrics
COLLECT_NETWORK_METRICS="true"

# Define metric names in config (easy to update)
METRIC_CPU_PERCENT="cpu_percent"
METRIC_MEMORY_PERCENT="memory_percent"
METRIC_STORAGE_PERCENT="storage_percent"
```

### **2. Single Generic Collection Function**
```bash
# One function handles all metric collection
collect_metric() {
    local resource_id="$1"
    local metric_name="$2"
    local start_time="$3"
    local end_time="$4"
    local display_name="${5:-$metric_name}"
    local aggregation="${6:-Average}"
    local interval="${7:-PT1H}"
    
    # Execute Azure CLI command
    local metric_data=$(az monitor metrics list \
        --resource "$resource_id" \
        --metric "$metric_name" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval "$interval" \
        --aggregation "$aggregation" \
        --output json 2>/dev/null)
    
    # Save raw JSON data (NO PROCESSING)
    echo "$metric_data" > "${SERVER_NAME}_${metric_name}_metrics.json"
}
```

### **3. JSON-Only Output with Summary**
```json
{
  "server_name": "mysql-server-01",
  "collection_timestamp": "2025-01-14 21:30:00 UTC",
  "time_period": "1h",
  "start_time": "2025-01-14T20:30:00Z",
  "end_time": "2025-01-14T21:30:00Z",
  "metrics_collected": {
    "cpu_metrics": "mysql-server-01_cpu_percent_metrics.json",
    "memory_metrics": "mysql-server-01_memory_percent_metrics.json",
    "storage_metrics": "mysql-server-01_storage_percent_metrics.json",
    "connection_metrics": "mysql-server-01_active_connections_metrics.json"
  },
  "metrics_collected_count": 4,
  "metrics_failed_count": 0
}
```

## 📊 **Benefits**

### **1. Separation of Concerns**
- ✅ **Collection Scripts**: Only gather raw data
- ✅ **Processing Scripts**: Handle analysis separately
- ✅ **Clear Responsibilities**: Each script has one job

### **2. Flexibility & Control**
- ✅ **Configurable**: Users control what metrics to collect
- ✅ **Efficient**: Skip unwanted metrics to save time
- ✅ **Customizable**: Easy to add new metrics via config

### **3. Code Reduction & Maintainability**
- ✅ **Less Code**: Remove all processing logic
- ✅ **Single Function**: One function handles all metrics
- ✅ **Easier Updates**: Change metric names in config only

### **4. Better Performance**
- ✅ **Faster Execution**: No processing overhead
- ✅ **Parallel Processing**: Can process multiple files later
- ✅ **Resource Efficient**: Lower memory usage

### **5. Future-Proof Architecture**
- ✅ **Scalable**: Easy to add new metrics
- ✅ **Testable**: Raw data can be tested independently
- ✅ **Reusable**: Same pattern across all scripts

## 🔄 **Migration Strategy**

### **Phase 1: Update Configuration**
1. Add metric collection flags to config files
2. Add metric name definitions to config files
3. Test configuration loading

### **Phase 2: Implement Generic Function**
1. Create `collect_metric()` function
2. Replace existing metric collection code
3. Test individual metric collection

### **Phase 3: Remove Processing Logic**
1. Remove all calculation and formatting code
2. Remove display/printing of processed values
3. Keep only raw JSON file creation

### **Phase 4: Add Summary Generation**
1. Create metrics summary JSON file
2. Include file paths for later processing
3. Add collection statistics

### **Phase 5: Testing & Validation**
1. Test all scripts with new architecture
2. Verify JSON file creation
3. Validate summary file accuracy

## 📁 **File Structure After Migration**
```
mysql/
├── azure_mysql_flexible_server_check.sh  # Pure data collection
├── test-config.conf                       # Enhanced with metric flags
├── mysql-server-01_cpu_percent_metrics.json
├── mysql-server-01_memory_percent_metrics.json
├── mysql-server-01_storage_percent_metrics.json
├── mysql-server-01_metrics_summary.json  # Summary with file paths
└── mysql-server-01_20250114_213000.zip   # All files zipped
```

## 🎯 **Next Steps**
1. **Approve Architecture**: Confirm this approach meets requirements
2. **Start with MySQL**: Implement as proof of concept
3. **Apply to All Scripts**: Replicate pattern across PostgreSQL, SQL Server, VM
4. **Create Processing Scripts**: Separate scripts for data analysis
5. **Documentation**: Update usage guides and examples

## ❓ **Questions for Consideration**
1. Should we keep any basic validation in collection scripts?
2. Do you want separate processing scripts or one unified processor?
3. Should the summary JSON include basic metadata about each metric?
4. Any specific metric categories you want to prioritize?

This architecture will make the scripts much cleaner, faster, and more maintainable while giving users full control over what data they collect.
