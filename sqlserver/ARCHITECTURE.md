# Azure SQL Database Monitoring Script Architecture

## Overview

The Azure SQL Database monitoring script provides comprehensive monitoring and metrics collection for Azure SQL Database instances, including single databases, elastic pools, and managed instances.

## Script Features

### 🎯 **Core Capabilities**
- **Single Database Monitoring** - Complete analysis of individual SQL databases
- **Elastic Pool Support** - Automatic detection and monitoring of elastic pool configurations
- **Server-Level Monitoring** - SQL Server firewall rules and configuration
- **25+ Performance Metrics** - Comprehensive database performance analysis
- **Structured Data Organization** - Organized output with server-info, database-info, and metrics directories

### 📊 **Metrics Collection**

#### **Core Performance Metrics**
- **CPU Percentage** - Database CPU utilization
- **Data IO Percentage** - Physical data read percentage
- **Log IO Percentage** - Transaction log write percentage
- **DTU Percentage** - Database Transaction Unit consumption
- **Storage Used/Percentage** - Database storage utilization
- **Memory Usage** - Database memory consumption

#### **Connection & Session Metrics**
- **Successful/Failed Connections** - Connection success and failure rates
- **Blocked by Firewall** - Firewall-blocked connection attempts
- **Sessions Percentage** - Active session utilization
- **Workers Percentage** - Worker thread utilization

#### **Advanced SQL Database Metrics**
- **Deadlocks** - Deadlock occurrence monitoring
- **In-Memory OLTP Storage** - OLTP storage percentage
- **TempDB Metrics** - TempDB data size, log size, and usage
- **App CPU/Memory** - Application-level resource consumption
- **SQL Instance Metrics** - Instance-level CPU and memory
- **Data Warehouse Metrics** - Backup and snapshot sizes for DW

### 🏗️ **Architecture Components**

#### **1. Configuration Management**
```bash
# Required Configuration
AZURE_SUBSCRIPTION_ID="your-subscription-id"
RESOURCE_GROUP_NAME="your-resource-group"
SERVER_NAME="your-sql-server-name"
DATABASE_NAME="your-database-name"

# Optional Configuration
METRICS_TIME_PERIOD="1h"        # Metrics collection period
FETCH_PARAMETERS="true"         # Collect configuration data
GENERATE_ZIP="false"            # Create ZIP archive
```

#### **2. Data Collection Flow**
1. **Authentication & Validation**
   - Azure CLI authentication check
   - Subscription and resource group validation
   - SQL Server and database existence verification

2. **Server & Database Analysis**
   - SQL Server details collection
   - Database configuration analysis
   - Elastic pool detection (if applicable)

3. **Metrics Collection**
   - Time-series metrics from Azure Monitor
   - 25+ database performance indicators
   - Configurable time periods (1h to 30d)

4. **Configuration Analysis**
   - Database configuration settings
   - Server firewall rules
   - Elastic pool details (if applicable)

#### **3. Output Organization**
```
sql-database-{SERVER}-{DATABASE}_{TIMESTAMP}/
├── summary.json                    # Consolidated summary
├── server-info/                    # Server-level information
│   ├── {SERVER}_{DATABASE}_server_details.json
│   ├── {SERVER}_firewall_rules.json
│   └── {SERVER}_{POOL}_pool_details.json (if applicable)
├── database-info/                  # Database-specific information
│   ├── {SERVER}_{DATABASE}_database_details.json
│   └── {SERVER}_{DATABASE}_configuration.json
└── metrics/                        # Performance metrics
    ├── {SERVER}_{DATABASE}_cpu_percent_metrics.json
    ├── {SERVER}_{DATABASE}_storage_metrics.json
    └── ... (25+ metric files)
```

### 🔧 **Technical Implementation**

#### **Metrics Collection Strategy**
- **Azure Monitor Integration** - Uses `az monitor metrics list` for data collection
- **Time Range Calculation** - Dynamic time range based on specified period
- **Error Handling** - Graceful degradation for unavailable metrics
- **Data Validation** - JSON validation and null value handling

#### **File Organization Logic**
- **Dynamic Discovery** - Automatically finds and organizes created files
- **Type-Based Sorting** - Files organized by content type (server, database, metrics)
- **Structured Hierarchy** - Clear separation of concerns in directory structure

#### **Archive Creation**
- **ZIP Support** - Primary archive format using zip utility
- **TAR.GZ Fallback** - Alternative format if zip unavailable
- **Cleanup Logic** - Removes original directory after successful archiving

### 🎯 **Use Cases**

#### **1. Performance Monitoring**
- Real-time database performance analysis
- Resource utilization tracking
- Bottleneck identification
- Capacity planning

#### **2. Configuration Auditing**
- Database configuration documentation
- Firewall rule analysis
- Elastic pool optimization
- Security compliance

#### **3. Troubleshooting**
- Performance issue diagnosis
- Connection problem analysis
- Resource constraint identification
- Historical trend analysis

#### **4. Migration Planning**
- Current state assessment
- Performance baseline establishment
- Resource requirement analysis
- Migration strategy development

### 🔒 **Security & Compliance**

#### **Authentication**
- Azure CLI-based authentication
- No credential storage in scripts
- Respects Azure RBAC permissions

#### **Data Handling**
- Read-only operations on Azure resources
- Local data storage only
- No sensitive data in logs
- Configurable data retention

### 🚀 **Execution Examples**

#### **Basic Monitoring**
```bash
./azure_sql_database_check.sh test-config.conf
```

#### **With ZIP Archive**
```bash
./azure_sql_database_check.sh test-config.conf --generate-zip
```

#### **Help Information**
```bash
./azure_sql_database_check.sh --help
```

### 📈 **Performance Considerations**

#### **Execution Time**
- **Small database**: ~30-60 seconds
- **With elastic pool**: ~1-2 minutes
- **Large metric collection**: ~2-3 minutes

#### **Data Volume**
- **Per database**: 15-30 JSON files
- **File sizes**: 1KB - 200KB per metric file
- **Total per database**: ~3-8MB uncompressed

### 🔄 **Integration Options**

#### **Automation**
- CI/CD pipeline integration
- Scheduled monitoring via cron
- PowerShell wrapper scripts
- Azure DevOps integration

#### **Data Analysis**
- JSON parsing for custom analysis
- Excel/CSV conversion for reporting
- Power BI integration for dashboards
- Grafana visualization

### 🛠️ **Troubleshooting**

#### **Common Issues**
- **Authentication failures** - Run `az login` and verify subscription
- **Resource not found** - Verify server and database names
- **Missing metrics** - Some metrics may not be available on all service tiers
- **Permission errors** - Ensure proper Azure RBAC permissions

#### **Debug Mode**
```bash
export DEBUG=true
./azure_sql_database_check.sh test-config.conf
```

## Summary

The Azure SQL Database monitoring script provides enterprise-grade monitoring capabilities with comprehensive metrics collection, structured data organization, and flexible output options. It's designed for production use with robust error handling, security best practices, and extensive customization options.
