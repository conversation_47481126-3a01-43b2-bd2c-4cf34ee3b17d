# Azure SQL Database Monitoring Script

Enterprise-grade monitoring script for Azure SQL Database with comprehensive metrics collection, server and database analysis, and professional logging.

## ✨ **Key Features**

- **🔧 JSON Configuration** - Modern, readable configuration format with auto-detection
- **📊 Dynamic Metrics Collection** - Configurable metrics list for future-proof monitoring
- **🕐 UTC + Local Timestamps** - Professional logging with timezone consistency
- **🏢 Server & Database Analysis** - Both server-level and database-level monitoring
- **📁 Organized Output** - Structured JSON files with summary reports
- **📦 ZIP Archive Support** - Compressed output for easy sharing
- **🎨 Enterprise Logging** - Color-coded output with detailed timestamps
- **🛡️ Error Resilience** - Robust error handling that continues on individual failures
- **🔄 Backward Compatibility** - Supports legacy .conf files

## 🚀 **Quick Start**

### **1. Prerequisites**
```bash
# Install Azure CLI
brew install azure-cli  # macOS
# or
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash  # Ubuntu/Debian

# Install jq for JSON processing
brew install jq  # macOS
# or  
sudo apt-get install jq  # Ubuntu/Debian

# Login to Azure
az login
```

### **2. Configuration**
Create a configuration file (e.g., `production.conf`):
```bash
# Azure Configuration
AZURE_SUBSCRIPTION_ID="your-subscription-id"
RESOURCE_GROUP_NAME="your-resource-group"
SERVER_NAME="your-sql-server-name"
DATABASE_NAME="your-database-name"

# Monitoring Configuration  
METRICS_TIME_PERIOD="1h"  # Options: 1h, 6h, 12h, 1d, 3d, 7d, 30d

# Server Parameters Configuration
FETCH_PARAMETERS="true"   # Set to "false" to skip parameter collection
```

### **3. Usage**
```bash
# With JSON configuration (recommended) - ZIP generated by default
bash azure_sql_database_check.sh --config-path config.json

# With legacy .conf configuration - ZIP generated by default
bash azure_sql_database_check.sh --config-path production.conf

# Disable ZIP generation (keep directory structure)
bash azure_sql_database_check.sh --config-path config.json --generate-zip false

# Get help
bash azure_sql_database_check.sh --help
```

## 📊 **Professional Logging Output**

The script provides enterprise-grade logging with UTC timestamps:

```bash
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Starting Azure SQL Database monitoring
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [SUCCESS] All required tools are available
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Reading configuration from: production.conf
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [SUCCESS] Configuration validated successfully
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Subscription ID: 207a84e2-208b-4809-baab-e94a043914f5
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Resource Group: production-rg
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Server Name: sql-prod-server
```

## 📁 **Output Structure**

### **Directory Organization:**
```
sql-database-myserver-mydatabase_20250714_131352/
├── summary.json                           # 📋 Consolidated summary report
├── server-info/                           # 🖥️ Server-level data
│   ├── myserver_server_details.json      # SQL Server configuration
│   ├── myserver_firewall_rules.json      # Firewall configuration
│   └── myserver_pool_details.json        # Elastic pool details (if applicable)
├── database-info/                         # 🗄️ Database-level data
│   ├── mydatabase_database_details.json  # Database configuration
│   └── mydatabase_configuration.json     # Database settings
└── metrics/                               # 📊 Performance metrics
    ├── myserver_cpu_metrics.json         # CPU utilization
    ├── myserver_dtu_metrics.json         # DTU consumption
    ├── myserver_storage_metrics.json     # Storage utilization
    ├── myserver_connection_metrics.json  # Connection statistics
    └── ... (10+ additional metric files)
```

## 📊 **Collected Metrics**

### **Core Performance Metrics:**
- **CPU Utilization** - Average CPU usage percentage
- **DTU Consumption** - Database Transaction Unit usage
- **Storage Utilization** - Storage usage percentage
- **Connection Count** - Active database connections
- **Data IO** - Data read/write operations
- **Log IO** - Transaction log operations
- **Memory Usage** - Memory consumption metrics

### **Advanced SQL Database Metrics:**
- **Deadlock Statistics** - Deadlock occurrences and analysis
- **Blocked Process** - Process blocking statistics
- **SQL Compilation** - Query compilation metrics
- **Batch Requests** - Batch execution statistics
- **Page Life Expectancy** - Buffer pool efficiency
- **Lock Statistics** - Lock waits and timeouts
- **Backup/Restore** - Backup operation metrics

### **Configuration Data:**
- **Server Details** - Edition, service tier, compute size
- **Database Configuration** - Collation, compatibility level, settings
- **Firewall Rules** - Network access configuration
- **Elastic Pool** - Pool configuration (if applicable)
- **Security Settings** - Authentication and authorization

## 🔧 **Advanced Configuration**

### **Time Period Options:**
```bash
METRICS_TIME_PERIOD="1h"   # Last 1 hour (default)
METRICS_TIME_PERIOD="6h"   # Last 6 hours  
METRICS_TIME_PERIOD="12h"  # Last 12 hours
METRICS_TIME_PERIOD="1d"   # Last 1 day
METRICS_TIME_PERIOD="3d"   # Last 3 days
METRICS_TIME_PERIOD="7d"   # Last 7 days
METRICS_TIME_PERIOD="30d"  # Last 30 days
```

### **Parameter Collection:**
```bash
FETCH_PARAMETERS="true"   # Collect all SQL Database parameters (recommended)
FETCH_PARAMETERS="false"  # Skip parameter collection (faster execution)
```

## 🛠️ **Troubleshooting**

### **Common Issues:**
1. **Azure CLI not found** - Install Azure CLI using prerequisites
2. **jq not found** - Install jq for JSON processing
3. **Permission denied** - Ensure proper Azure RBAC permissions
4. **Server not found** - Verify server name and resource group
5. **Database not found** - Check database name and server
6. **Subscription access** - Check subscription ID and access rights

### **Error Handling:**
The script includes robust error handling:
- **Continues on individual metric failures** - Won't stop if one metric fails
- **Graceful file operation handling** - Handles file move failures safely
- **Clear error messages** - Detailed logging for troubleshooting
- **Validation at each step** - Comprehensive input validation

## 📈 **Enterprise Benefits**

- **Global Team Coordination** - UTC timestamps for multi-timezone teams
- **Audit Compliance** - Comprehensive logging with timestamps
- **Performance Monitoring** - Historical metrics for trend analysis  
- **Capacity Planning** - DTU, storage, and connection utilization data
- **Security Analysis** - Firewall and access configuration review
- **Configuration Management** - Complete server and database documentation
- **Incident Response** - Detailed system state capture

Perfect for **production environments**, **compliance audits**, and **performance optimization**! 🌟
