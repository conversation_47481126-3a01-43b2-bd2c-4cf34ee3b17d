{"azure": {"subscription_id": "207a84e2-208b-4809-baab-e94a043914f5", "resource_group": "amit-test", "server_name": "amit-server-test", "database_name": "amit-sql-database"}, "monitoring": {"metrics_time_period": "1h", "_supported_values": "1h, 6h, 12h, 1d, 3d, 7d, 30d"}, "collection": {"fetch_parameters": true, "collect_metrics": true, "metrics_list": ["cpu_percent", "physical_data_read_percent", "log_write_percent", "dtu_consumption_percent", "storage", "connection_successful", "connection_failed", "blocked_by_firewall", "deadlock", "storage_percent", "xtp_storage_percent", "workers_percent", "sessions_percent", "dtu_limit", "dtu_used", "cpu_limit", "cpu_used", "sqlserver_process_core_percent", "sqlserver_process_memory_percent", "tempdb_data_size", "tempdb_log_size", "tempdb_log_used_percent", "app_cpu_billed", "app_cpu_percent", "app_memory_percent"]}}