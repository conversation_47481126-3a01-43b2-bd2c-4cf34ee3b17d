#!/bin/bash

# Azure SQL Database Monitoring Script
# Version: 1.0
# Description: Comprehensive monitoring and metrics collection for Azure SQL Database
#              Supports single databases, elastic pools, and managed instances
#
# USAGE:
#   bash azure_sql_database_check.sh --config-path <config-file> [--generate-zip <true|false>]
#
# EXAMPLES:
#   # JSON configuration (recommended) - ZIP generated by default
#   bash azure_sql_database_check.sh --config-path config.json
#
#   # Legacy .conf configuration - ZIP generated by default
#   bash azure_sql_database_check.sh --config-path config.conf
#
#   # Disable ZIP generation (keep directory structure)
#   bash azure_sql_database_check.sh --config-path config.json --generate-zip false
#
# JSON CONFIGURATION FORMAT:
#   {
#     "azure": {
#       "subscription_id": "your-subscription-id",
#       "resource_group": "your-resource-group",
#       "server_name": "your-sql-server",
#       "database_name": "your-database"
#     },
#     "monitoring": {
#       "metrics_time_period": "1h"
#     },
#     "collection": {
#       "fetch_parameters": true,
#       "collect_metrics": true,
#       "metrics_list": ["cpu_percent", "dtu_consumption_percent", "storage_percent"]
#     }
#   }
#
# LEGACY .CONF FORMAT (still supported):
#   AZURE_SUBSCRIPTION_ID="your-subscription-id"
#   RESOURCE_GROUP_NAME="your-resource-group"
#   SERVER_NAME="your-sql-server"
#   DATABASE_NAME="your-database"
#   METRICS_TIME_PERIOD="1h"
#   FETCH_PARAMETERS="true"
#   COLLECT_METRICS="true"

set -uo pipefail
# Note: Removed -e to allow graceful error handling and continuation

# Script metadata
readonly SCRIPT_VERSION="1.0"
readonly SCRIPT_NAME="Azure SQL Database Monitor"

# Global variables
AZURE_SUBSCRIPTION_ID=""
RESOURCE_GROUP_NAME=""
SERVER_NAME=""
DATABASE_NAME=""
METRICS_TIME_PERIOD="1h"
FETCH_PARAMETERS="false"
GENERATE_ZIP="true"
OUTPUT_DIRECTORY=""

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Function to get timestamp with both local and UTC time
get_timestamp() {
    local local_time=$(date +"%Y-%m-%d %H:%M:%S")
    local utc_time=$(date -u +"%Y-%m-%d %H:%M:%S")
    echo "${local_time} (UTC: ${utc_time})"
}

# Logging functions with timestamps
log_info() {
    echo -e "${BLUE}[$(get_timestamp)] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(get_timestamp)] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(get_timestamp)] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(get_timestamp)] [ERROR]${NC} $1"
}

# Function to execute Azure CLI commands safely with error handling
safe_az_command() {
    local command_description="$1"
    shift
    local az_command=("$@")

    log_info "Executing: $command_description" >&2

    local output
    local exit_code

    # Execute the command and capture both output and exit code
    if output=$("${az_command[@]}" 2>&1); then
        exit_code=0
        echo "$output"
        return 0
    else
        exit_code=$?
        log_error "Failed: $command_description" >&2
        log_error "Command: ${az_command[*]}" >&2
        log_error "Error output: $output" >&2
        log_warning "Continuing with next operation..." >&2
        # Return empty output to avoid jq parse errors
        echo ""
        return $exit_code
    fi
}

# Function to check prerequisites
check_prerequisites() {
    local missing_tools=()
    
    # Check for required tools
    if ! command -v az &> /dev/null; then
        missing_tools+=("az (Azure CLI)")
    fi
    
    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
    fi
    
    if ! command -v date &> /dev/null; then
        missing_tools+=("date")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install the missing tools and try again"
        return 1
    fi
    
    log_success "All required tools are available"
    return 0
}

# Function to validate time period
validate_time_period() {
    local period="$1"
    case "$period" in
        1h|6h|12h|1d|3d|7d|30d)
            return 0
            ;;
        *)
            log_error "Invalid time period: $period"
            log_info "Supported values: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
            return 1
            ;;
    esac
}

# Function to load JSON configuration file
load_json_config() {
    local json_file="$1"

    # Check if jq is available
    if ! command -v jq >/dev/null 2>&1; then
        log_error "jq is required to parse JSON configuration files"
        log_info "Please install jq or use .conf format instead"
        exit 1
    fi

    # Validate JSON syntax
    if ! jq empty "$json_file" 2>/dev/null; then
        log_error "Invalid JSON syntax in config file: $json_file"
        exit 1
    fi

    # Extract configuration values from JSON
    AZURE_SUBSCRIPTION_ID=$(jq -r '.azure.subscription_id // empty' "$json_file")
    RESOURCE_GROUP_NAME=$(jq -r '.azure.resource_group // empty' "$json_file")
    SERVER_NAME=$(jq -r '.azure.server_name // empty' "$json_file")
    DATABASE_NAME=$(jq -r '.azure.database_name // empty' "$json_file")

    METRICS_TIME_PERIOD=$(jq -r '.monitoring.metrics_time_period // "1h"' "$json_file")

    FETCH_PARAMETERS=$(jq -r '.collection.fetch_parameters // true' "$json_file")
    COLLECT_METRICS=$(jq -r '.collection.collect_metrics // true' "$json_file")

    # Convert metrics_list array to comma-separated string
    local metrics_array=$(jq -r '.collection.metrics_list[]?' "$json_file" 2>/dev/null)
    if [[ -n "$metrics_array" ]]; then
        METRICS_LIST=$(echo "$metrics_array" | tr '\n' ',' | sed 's/,$//')
    else
        METRICS_LIST=""
    fi

    log_info "Successfully loaded JSON configuration"
}

# Function to validate configuration
validate_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "Configuration file not found: $config_file"
        return 1
    fi
    
    log_info "Reading configuration from: $config_file"
    
    # Preserve command-line arguments before sourcing config file
    local preserve_generate_zip="$GENERATE_ZIP"

    # Determine config file format and load accordingly
    if [[ "$config_file" == *.json ]]; then
        load_json_config "$config_file"
    else
        # Source the config file (legacy .conf format)
        # shellcheck source=/dev/null
        source "$config_file"
    fi

    # Restore command-line arguments if they were set
    if [[ -n "$preserve_generate_zip" ]]; then
        GENERATE_ZIP="$preserve_generate_zip"
    fi
    
    # Validate required variables
    if [[ -z "$AZURE_SUBSCRIPTION_ID" ]]; then
        log_error "AZURE_SUBSCRIPTION_ID is not set in configuration file"
        return 1
    fi
    
    if [[ -z "$RESOURCE_GROUP_NAME" ]]; then
        log_error "RESOURCE_GROUP_NAME is not set in configuration file"
        return 1
    fi
    
    if [[ -z "$SERVER_NAME" ]]; then
        log_error "SERVER_NAME is not set in configuration file"
        return 1
    fi
    
    if [[ -z "$DATABASE_NAME" ]]; then
        log_error "DATABASE_NAME is not set in configuration file"
        return 1
    fi
    
    # Validate time period if specified
    if [[ -n "$METRICS_TIME_PERIOD" ]]; then
        if ! validate_time_period "$METRICS_TIME_PERIOD"; then
            return 1
        fi
        log_success "Valid time period: $METRICS_TIME_PERIOD"
    fi
    
    # Set defaults for optional variables (but don't override command line arguments)
    FETCH_PARAMETERS="${FETCH_PARAMETERS:-false}"
    COLLECT_METRICS="${COLLECT_METRICS:-true}"
    METRICS_LIST="${METRICS_LIST:-cpu_percent,physical_data_read_percent,log_write_percent,dtu_consumption_percent,storage,connection_successful}"
    GENERATE_ZIP="${GENERATE_ZIP:-true}"
    
    log_success "Configuration validated successfully"
    return 0
}

# Function to check Azure login status
check_azure_login() {
    log_info "Checking Azure login status..."
    
    if ! az account show &>/dev/null; then
        log_error "Not logged into Azure. Please run 'az login' first"
        return 1
    fi
    
    log_success "Currently logged into Azure"
    
    # Get current subscription
    local current_subscription
    current_subscription=$(az account show --query id --output tsv 2>/dev/null)
    log_info "Current subscription: $current_subscription"
    
    # Set subscription if different
    if [[ "$current_subscription" != "$AZURE_SUBSCRIPTION_ID" ]]; then
        log_info "Switching to subscription: $AZURE_SUBSCRIPTION_ID"
        if ! az account set --subscription "$AZURE_SUBSCRIPTION_ID"; then
            log_error "Failed to set subscription: $AZURE_SUBSCRIPTION_ID"
            return 1
        fi
        log_success "Switched to subscription: $AZURE_SUBSCRIPTION_ID"
    else
        log_success "Already using the correct subscription"
    fi
    
    return 0
}

# Function to validate resource group
validate_resource_group() {
    log_info "Validating resource group: $RESOURCE_GROUP_NAME"
    
    if ! az group show --name "$RESOURCE_GROUP_NAME" &>/dev/null; then
        log_error "Resource group '$RESOURCE_GROUP_NAME' not found"
        return 1
    fi
    
    log_success "Resource group '$RESOURCE_GROUP_NAME' exists"
    
    # Get resource group location
    local location
    location=$(az group show --name "$RESOURCE_GROUP_NAME" --query location --output tsv 2>/dev/null)
    log_info "Resource group location: $location"
    
    return 0
}

# Function to check SQL Server and Database
check_sql_server_database() {
    log_info "Checking SQL Server: $SERVER_NAME"
    
    # Check if SQL Server exists
    if ! az sql server show --name "$SERVER_NAME" --resource-group "$RESOURCE_GROUP_NAME" &>/dev/null; then
        log_error "SQL Server '$SERVER_NAME' not found in resource group '$RESOURCE_GROUP_NAME'"
        
        # List available SQL Servers
        log_info "Available SQL Servers in resource group:"
        az sql server list --resource-group "$RESOURCE_GROUP_NAME" --query "[].name" --output table 2>/dev/null || echo "None found"
        return 1
    fi
    
    log_success "SQL Server '$SERVER_NAME' found"
    
    log_info "Checking SQL Database: $DATABASE_NAME"
    
    # Check if database exists
    if ! az sql db show --name "$DATABASE_NAME" --server "$SERVER_NAME" --resource-group "$RESOURCE_GROUP_NAME" &>/dev/null; then
        log_error "SQL Database '$DATABASE_NAME' not found on server '$SERVER_NAME'"
        
        # List available databases
        log_info "Available databases on server '$SERVER_NAME':"
        az sql db list --server "$SERVER_NAME" --resource-group "$RESOURCE_GROUP_NAME" --query "[].name" --output table 2>/dev/null || echo "None found"
        return 1
    fi
    
    log_success "SQL Database '$DATABASE_NAME' found"
    return 0
}

# Function to get server and database details
get_server_database_details() {
    log_info "Fetching comprehensive server and database information..."
    
    # Get SQL Server details
    local server_details
    local server_exit_code
    server_details=$(safe_az_command "Getting SQL Server details" \
        az sql server show \
        --name "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json)
    server_exit_code=$?

    if [[ $server_exit_code -eq 0 && -n "$server_details" && "$server_details" != "null" && "$server_details" != *"ERROR"* ]]; then
        echo "$server_details" > "${SERVER_NAME}_${DATABASE_NAME}_server_details.json"
        log_success "Server details saved to: ${SERVER_NAME}_${DATABASE_NAME}_server_details.json"
    fi
    
    # Get SQL Database details
    local database_details
    local database_exit_code
    database_details=$(safe_az_command "Getting SQL Database details" \
        az sql db show \
        --name "$DATABASE_NAME" \
        --server "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json)
    database_exit_code=$?

    if [[ $database_exit_code -eq 0 && -n "$database_details" && "$database_details" != "null" && "$database_details" != *"ERROR"* ]]; then
        echo "$database_details" > "${SERVER_NAME}_${DATABASE_NAME}_database_details.json"
        log_success "Database details saved to: ${SERVER_NAME}_${DATABASE_NAME}_database_details.json"
    fi
    
    # Display key information
    echo ""
    echo "=================================================="
    echo "         SQL SERVER & DATABASE DETAILS"
    echo "=================================================="
    
    # Server information
    local server_location server_version admin_login
    server_location=$(echo "$server_details" | jq -r '.location // "N/A"')
    server_version=$(echo "$server_details" | jq -r '.version // "N/A"')
    admin_login=$(echo "$server_details" | jq -r '.administratorLogin // "N/A"')
    
    printf "%-25s: %s\n" "Server Name" "$SERVER_NAME"
    printf "%-25s: %s\n" "Server Location" "$server_location"
    printf "%-25s: %s\n" "Server Version" "$server_version"
    printf "%-25s: %s\n" "Admin Login" "$admin_login"
    
    echo ""
    echo "DATABASE DETAILS:"
    
    # Database information
    local db_status db_edition db_service_tier db_sku max_size_bytes
    db_status=$(echo "$database_details" | jq -r '.status // "N/A"')
    db_edition=$(echo "$database_details" | jq -r '.edition // "N/A"')
    db_service_tier=$(echo "$database_details" | jq -r '.currentServiceObjectiveName // "N/A"')
    db_sku=$(echo "$database_details" | jq -r '.sku.name // "N/A"')
    max_size_bytes=$(echo "$database_details" | jq -r '.maxSizeBytes // "N/A"')
    
    printf "%-25s: %s\n" "Database Name" "$DATABASE_NAME"
    printf "%-25s: %s\n" "Status" "$db_status"
    printf "%-25s: %s\n" "Edition" "$db_edition"
    printf "%-25s: %s\n" "Service Tier" "$db_service_tier"
    printf "%-25s: %s\n" "SKU" "$db_sku"
    
    # Convert max size to readable format
    if [[ "$max_size_bytes" != "N/A" && "$max_size_bytes" != "null" ]]; then
        local max_size_gb=$((max_size_bytes / 1024 / 1024 / 1024))
        printf "%-25s: %s GB\n" "Max Size" "$max_size_gb"
    else
        printf "%-25s: %s\n" "Max Size" "N/A"
    fi
    
    echo "=================================================="
}

# Function to collect database metrics
collect_database_metrics() {
    if [[ -z "$METRICS_TIME_PERIOD" ]]; then
        log_info "Skipping metrics collection (no time period specified)"
        return 0
    fi

    log_info "Collecting database metrics for time period: $METRICS_TIME_PERIOD"

    # Calculate time range (compatible with both GNU and BSD date)
    local end_time start_time
    end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

    case "$METRICS_TIME_PERIOD" in
        1h)  start_time=$(date -u -v-1H +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '1 hour ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        6h)  start_time=$(date -u -v-6H +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '6 hours ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        12h) start_time=$(date -u -v-12H +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '12 hours ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        1d)  start_time=$(date -u -v-1d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '1 day ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        3d)  start_time=$(date -u -v-3d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '3 days ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        7d)  start_time=$(date -u -v-7d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '7 days ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        30d) start_time=$(date -u -v-30d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '30 days ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        *)
            log_error "Invalid time period: $METRICS_TIME_PERIOD"
            return 1
            ;;
    esac

    # Fallback if both date formats fail
    if [[ -z "$start_time" ]]; then
        log_error "Failed to calculate start time for period: $METRICS_TIME_PERIOD"
        log_info "Your system's date command may not support the required options"
        return 1
    fi

    log_info "Metrics time range: $start_time to $end_time"

    # Also try a longer time period if the current one shows no activity
    local extended_start_time
    case "$METRICS_TIME_PERIOD" in
        1h)  extended_start_time=$(date -u -v-24H +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '24 hours ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        6h)  extended_start_time=$(date -u -v-7d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '7 days ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        *)   extended_start_time="$start_time" ;;
    esac

    if [[ "$extended_start_time" != "$start_time" ]]; then
        log_info "Extended time range available: $extended_start_time to $end_time (for fallback if no recent activity)"
    fi

    # Get database resource ID
    local resource_id
    resource_id="/subscriptions/$AZURE_SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP_NAME/providers/Microsoft.Sql/servers/$SERVER_NAME/databases/$DATABASE_NAME"

    echo ""
    echo "=================================================="
    echo "           COLLECTING METRICS"
    echo "=================================================="

    # Comprehensive SQL Database metrics - organized by category
    local core_metrics=(
        "cpu_percent:CPU Percentage"
        "physical_data_read_percent:Data IO Percentage"
        "log_write_percent:Log IO Percentage"
        "storage:Storage Used (Bytes)"
        "storage_percent:Storage Percentage"
        "allocated_data_storage:Allocated Data Storage"
    )

    local connection_metrics=(
        "connection_successful:Successful Connections"
        "connection_failed:Failed Connections"
        "connection_failed_user_error:Failed Connections (User Error)"
        "blocked_by_firewall:Blocked by Firewall"
        "sessions_count:Active Sessions Count"
        "sessions_percent:Sessions Percentage"
        "workers_percent:Workers Percentage"
    )

    local performance_metrics=(
        "cpu_limit:CPU Limit"
        "cpu_used:CPU Used"
        "deadlock:Deadlocks"
        "sqlserver_process_core_percent:SQL Server Process Core Percent"
        "sqlserver_process_memory_percent:SQL Server Process Memory Percent"
        "sql_instance_cpu_percent:SQL Instance CPU Percent"
        "sql_instance_memory_percent:SQL Instance Memory Percent"
        "app_cpu_billed:App CPU Billed"
        "app_cpu_percent:App CPU Percentage"
        "app_memory_percent:App Memory Percentage"
    )

    local storage_metrics=(
        "storage_percent:Storage Percentage"
        "allocated_data_storage:Allocated Data Storage"
    )

    local advanced_metrics=(
        "xtp_storage_percent:In-Memory OLTP Storage Percentage"
        "availability:Availability"
    )

    local serverless_metrics=(
        "app_cpu_billed:App CPU Billed (Serverless)"
        "app_memory_percent:App Memory Percent (Serverless)"
        "auto_pause_delay:Auto Pause Delay"
    )

    local elastic_pool_metrics=(
        "eDTU_limit:Elastic Pool eDTU Limit"
        "eDTU_used:Elastic Pool eDTU Used"
        "storage_limit:Elastic Pool Storage Limit"
        "storage_used:Elastic Pool Storage Used"
        "storage_percent:Elastic Pool Storage Percent"
        "physical_data_read_percent:Elastic Pool Data Read Percent"
        "log_write_percent:Elastic Pool Log Write Percent"
        "cpu_percent:Elastic Pool CPU Percent"
        "sessions_percent:Elastic Pool Sessions Percent"
        "workers_percent:Elastic Pool Workers Percent"
    )

    # Collect metrics by category
    echo "CORE PERFORMANCE METRICS:"
    echo "=================================================="
    collect_metric_category "${core_metrics[@]}"

    echo ""
    echo "CONNECTION & SESSION METRICS:"
    echo "=================================================="
    collect_metric_category "${connection_metrics[@]}"

    echo ""
    echo "PERFORMANCE & RESOURCE METRICS:"
    echo "=================================================="
    collect_metric_category "${performance_metrics[@]}"

    echo ""
    echo "STORAGE & BACKUP METRICS:"
    echo "=================================================="
    collect_metric_category "${storage_metrics[@]}"

    echo ""
    echo "ADVANCED FEATURES METRICS:"
    echo "=================================================="
    collect_metric_category "${advanced_metrics[@]}"
}

# Function to collect metrics for a specific category
collect_metric_category() {
    local metrics=("$@")
    local resource_id="/subscriptions/$AZURE_SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP_NAME/providers/Microsoft.Sql/servers/$SERVER_NAME/databases/$DATABASE_NAME"

    for metric_info in "${metrics[@]}"; do
        local metric_name="${metric_info%%:*}"
        local metric_display="${metric_info##*:}"

        log_info "Collecting $metric_display ($metric_name)..."

        # Try multiple aggregation types for better data coverage
        local aggregations=("Average" "Maximum" "Total" "Count")
        local metric_collected=false

        for aggregation in "${aggregations[@]}"; do
            local metric_data
            local az_exit_code
            metric_data=$(safe_az_command "Collecting metric: $metric_display ($aggregation)" \
                az monitor metrics list \
                --resource "$resource_id" \
                --metric "$metric_name" \
                --start-time "$start_time" \
                --end-time "$end_time" \
                --interval PT5M \
                --aggregation "$aggregation" \
                --output json)
            az_exit_code=$?

            # Check if the Azure CLI command succeeded and returned valid data
            if [[ $az_exit_code -eq 0 && -n "$metric_data" && "$metric_data" != "null" && "$metric_data" != *"ERROR"* ]]; then
                # Check if we actually have data points
                local data_points
                data_points=$(echo "$metric_data" | jq -r '.value[0].timeseries[0].data | length' 2>/dev/null)

                if [[ -n "$data_points" && "$data_points" -gt 0 ]]; then
                    echo "$metric_data" > "${SERVER_NAME}_${DATABASE_NAME}_${metric_name}_${aggregation,,}_metrics.json"

                    # Calculate value based on aggregation type
                    local value
                    case "$aggregation" in
                        "Average")
                            value=$(echo "$metric_data" | jq -r '.value[0].timeseries[0].data[]? | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
                            ;;
                        "Maximum")
                            value=$(echo "$metric_data" | jq -r '.value[0].timeseries[0].data[]? | select(.maximum != null) | .maximum' | awk 'BEGIN{max=-999999} {if($1>max) max=$1} END {if(max!=-999999) printf "%.2f", max; else print "N/A"}')
                            ;;
                        "Total")
                            value=$(echo "$metric_data" | jq -r '.value[0].timeseries[0].data[]? | select(.total != null) | .total' | awk '{sum+=$1} END {if(NR>0) printf "%.0f", sum; else print "N/A"}')
                            ;;
                        "Count")
                            value=$(echo "$metric_data" | jq -r '.value[0].timeseries[0].data[]? | select(.count != null) | .count' | awk '{sum+=$1} END {if(NR>0) printf "%.0f", sum; else print "N/A"}')
                            ;;
                    esac

                    if [[ "$value" != "N/A" && "$value" != "0.00" ]]; then
                        printf "  ✓ %-30s: %s (%s)\n" "$metric_display" "$value" "$aggregation"
                        metric_collected=true
                        break  # Found data, no need to try other aggregations
                    fi
                fi
            else
                # Azure CLI command failed or returned invalid data
                log_warning "Failed to collect metric: $metric_display ($aggregation)"
                printf "  ✗ %-30s: Collection failed\n" "$metric_display"
            fi
        done

        if [[ "$metric_collected" == false ]]; then
            printf "  ⚠ %-30s: No data available\n" "$metric_display"
        fi
    done

    echo "=================================================="
    log_success "Metrics collection completed"
}

# Function to collect any metric
collect_metric() {
    local metric_name="$1"
    local resource_id="$2"
    local start_time="$3"
    local end_time="$4"

    # Try to collect the metric
    az monitor metrics list \
        --resource "$resource_id" \
        --metric "$metric_name" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval "PT5M" \
        --aggregation "Average" \
        --output json > "${DATABASE_NAME}_${metric_name}_metrics.json" 2>/dev/null

    # Check if file was created and has content
    if [[ -f "${DATABASE_NAME}_${metric_name}_metrics.json" && -s "${DATABASE_NAME}_${metric_name}_metrics.json" ]]; then
        return 0
    else
        rm -f "${DATABASE_NAME}_${metric_name}_metrics.json" 2>/dev/null
        return 1
    fi
}

# New efficient metrics collection function
collect_database_metrics_new() {
    if [[ -z "$METRICS_TIME_PERIOD" ]]; then
        log_info "Skipping metrics collection (no time period specified)"
        return 0
    fi

    log_info "Collecting database metrics for time period: $METRICS_TIME_PERIOD"

    # Calculate time range (compatible with both GNU and BSD date)
    local end_time start_time
    end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

    case "$METRICS_TIME_PERIOD" in
        1h)  start_time=$(date -u -v-1H +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '1 hour ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        6h)  start_time=$(date -u -v-6H +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '6 hours ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        12h) start_time=$(date -u -v-12H +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '12 hours ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        1d)  start_time=$(date -u -v-1d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '1 day ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        3d)  start_time=$(date -u -v-3d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '3 days ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        7d)  start_time=$(date -u -v-7d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '7 days ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        30d) start_time=$(date -u -v-30d +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -u -d '30 days ago' +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null) ;;
        *)
            log_error "Invalid time period: $METRICS_TIME_PERIOD"
            return 1
            ;;
    esac

    # Fallback if both date formats fail
    if [[ -z "$start_time" ]]; then
        log_error "Failed to calculate start time for metrics collection"
        return 1
    fi

    log_info "Metrics time range: $start_time to $end_time"

    # Build resource ID
    local resource_id="/subscriptions/$AZURE_SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP_NAME/providers/Microsoft.Sql/servers/$SERVER_NAME/databases/$DATABASE_NAME"

    # Collect metrics from METRICS_LIST
    IFS=',' read -ra METRICS_ARRAY <<< "$METRICS_LIST"
    local metrics_collected=0
    local metrics_failed=0

    for metric_name in "${METRICS_ARRAY[@]}"; do
        metric_name=$(echo "$metric_name" | xargs)  # Trim whitespace
        [[ -z "$metric_name" ]] && continue

        log_info "Collecting metric: $metric_name"
        if collect_metric "$metric_name" "$resource_id" "$start_time" "$end_time"; then
            log_success "✓ Collected: $metric_name"
            ((metrics_collected++))
        else
            log_warning "⚠ Failed: $metric_name"
            ((metrics_failed++))
        fi
    done

    log_success "Metrics collection completed: $metrics_collected collected, $metrics_failed failed"
}

# Function to collect additional database insights
collect_database_insights() {
    log_info "Collecting additional database insights..."

    echo ""
    echo "=================================================="
    echo "           DATABASE INSIGHTS & ANALYSIS"
    echo "=================================================="

    # Get database usage and performance insights
    log_info "Fetching database usage statistics..."

    # Get database size and space usage
    local db_usage
    db_usage=$(safe_az_command "Getting database usage statistics" \
        az sql db show-usage \
        --name "$DATABASE_NAME" \
        --server "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json)

    if [[ -n "$db_usage" && "$db_usage" != "null" ]]; then
        echo "$db_usage" > "${SERVER_NAME}_${DATABASE_NAME}_usage_statistics.json"
        log_success "Database usage statistics saved"

        # Display key usage metrics
        echo "DATABASE USAGE STATISTICS:"
        echo "$db_usage" | jq -r '.[] | "  \(.displayName): \(.currentValue) \(.unit) (Limit: \(.limit) \(.unit))"' 2>/dev/null || echo "  Usage data format not recognized"
    else
        log_warning "Database usage statistics not available"
    fi

    # Get database audit settings
    log_info "Fetching database audit configuration..."
    local audit_policy
    audit_policy=$(safe_az_command "Getting database audit policy" \
        az sql db audit-policy show \
        --name "$DATABASE_NAME" \
        --server "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json)

    if [[ -n "$audit_policy" && "$audit_policy" != "null" ]]; then
        echo "$audit_policy" > "${SERVER_NAME}_${DATABASE_NAME}_audit_policy.json"
        log_success "Audit policy saved"

        local audit_state
        audit_state=$(echo "$audit_policy" | jq -r '.state // "Unknown"')
        printf "%-25s: %s\n" "Audit State" "$audit_state"
    fi

    # Get database threat detection settings
    log_info "Fetching threat detection configuration..."
    local threat_detection
    threat_detection=$(safe_az_command "Getting threat detection policy" \
        az sql db threat-policy show \
        --name "$DATABASE_NAME" \
        --server "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json)

    if [[ -n "$threat_detection" && "$threat_detection" != "null" ]]; then
        echo "$threat_detection" > "${SERVER_NAME}_${DATABASE_NAME}_threat_detection.json"
        log_success "Threat detection policy saved"

        local threat_state
        threat_state=$(echo "$threat_detection" | jq -r '.state // "Unknown"')
        printf "%-25s: %s\n" "Threat Detection" "$threat_state"
    fi

    # Get database transparent data encryption status
    log_info "Fetching transparent data encryption status..."
    local tde_status
    tde_status=$(safe_az_command "Getting transparent data encryption status" \
        az sql db tde show \
        --database "$DATABASE_NAME" \
        --server "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json)

    if [[ -n "$tde_status" && "$tde_status" != "null" ]]; then
        echo "$tde_status" > "${SERVER_NAME}_${DATABASE_NAME}_tde_status.json"
        log_success "TDE status saved"

        local tde_state
        tde_state=$(echo "$tde_status" | jq -r '.status // "Unknown"')
        printf "%-25s: %s\n" "TDE Status" "$tde_state"
    fi

    echo "=================================================="
    log_success "Database insights collection completed"
}

# Function to fetch database parameters/configuration
fetch_database_parameters() {
    if [[ "$FETCH_PARAMETERS" != "true" ]]; then
        log_info "Skipping database parameters collection (FETCH_PARAMETERS=false)"
        return 0
    fi

    log_info "Fetching SQL Database configuration..."

    # Get database configuration
    local db_config
    db_config=$(safe_az_command "Getting database configuration" \
        az sql db show \
        --name "$DATABASE_NAME" \
        --server "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json)

    if [[ -n "$db_config" && "$db_config" != "null" ]]; then
        echo "$db_config" > "${SERVER_NAME}_${DATABASE_NAME}_configuration.json"
        log_success "Database configuration saved to: ${SERVER_NAME}_${DATABASE_NAME}_configuration.json"
    fi

    # Get server firewall rules
    log_info "Fetching server firewall rules..."
    local firewall_rules
    firewall_rules=$(safe_az_command "Getting server firewall rules" \
        az sql server firewall-rule list \
        --server "$SERVER_NAME" \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --output json)

    if [[ -n "$firewall_rules" && "$firewall_rules" != "null" ]]; then
        echo "$firewall_rules" > "${SERVER_NAME}_firewall_rules.json"
        log_success "Firewall rules saved to: ${SERVER_NAME}_firewall_rules.json"
    fi

    # Get elastic pool information if database is in a pool
    local elastic_pool
    elastic_pool=$(echo "$db_config" | jq -r '.elasticPoolName // empty')

    if [[ -n "$elastic_pool" ]]; then
        log_info "Database is in elastic pool: $elastic_pool"
        log_info "Fetching elastic pool details..."

        local pool_details
        pool_details=$(safe_az_command "Getting elastic pool details" \
            az sql elastic-pool show \
            --name "$elastic_pool" \
            --server "$SERVER_NAME" \
            --resource-group "$RESOURCE_GROUP_NAME" \
            --output json)

        if [[ -n "$pool_details" && "$pool_details" != "null" ]]; then
            echo "$pool_details" > "${SERVER_NAME}_${elastic_pool}_pool_details.json"
            log_success "Elastic pool details saved to: ${SERVER_NAME}_${elastic_pool}_pool_details.json"
        fi
    fi
}

# Function to organize JSON files into directory structure
organize_json_files() {
    local timestamp
    timestamp=$(date +"%Y%m%d_%H%M%S")
    OUTPUT_DIRECTORY="sql-database-${SERVER_NAME}-${DATABASE_NAME}_${timestamp}"

    log_info "Organizing JSON files into directory: $OUTPUT_DIRECTORY"

    # Create main directory
    if ! mkdir -p "$OUTPUT_DIRECTORY"; then
        log_error "Failed to create directory: $OUTPUT_DIRECTORY"
        return 1
    fi
    log_success "Created directory: $OUTPUT_DIRECTORY"

    # Create subdirectories
    local server_dir="$OUTPUT_DIRECTORY/server-info"
    local database_dir="$OUTPUT_DIRECTORY/database-info"
    local metrics_dir="$OUTPUT_DIRECTORY/metrics"

    mkdir -p "$server_dir" "$database_dir" "$metrics_dir"
    log_success "Created subdirectories: server-info, database-info, metrics"

    # Move files to appropriate directories
    log_info "Discovering and moving created JSON files..."

    local files_moved=0

    # Find all JSON files that match the pattern
    local json_files_found=()
    while IFS= read -r -d '' file; do
        local filename=$(basename "$file")
        json_files_found+=("$filename")
    done < <(find . -maxdepth 1 -name "${SERVER_NAME}_*.json" -print0 2>/dev/null)

    log_info "Found ${#json_files_found[@]} JSON files to organize"

    # Organize files by type (temporarily disable errexit for file operations)
    set +e  # Disable exit on error temporarily
    for json_file in "${json_files_found[@]}"; do
        if [[ -f "$json_file" ]]; then
            local target_dir=""

            # Determine target directory based on file type
            if [[ "$json_file" == *"_server_details.json" || "$json_file" == *"_firewall_rules.json" || "$json_file" == *"_pool_details.json" ]]; then
                target_dir="$server_dir"
            elif [[ "$json_file" == *"_database_details.json" || "$json_file" == *"_configuration.json" ]]; then
                target_dir="$database_dir"
            elif [[ "$json_file" == *"_metrics.json" ]]; then
                target_dir="$metrics_dir"
            else
                target_dir="$OUTPUT_DIRECTORY"  # Default to main directory
            fi

            if mv "$json_file" "$target_dir/" 2>/dev/null; then
                log_success "Moved file: $json_file -> $target_dir/"
                ((files_moved++))
            else
                log_error "Failed to move file: $json_file"
                # Continue with other files instead of exiting
            fi
        fi
    done
    set -e  # Re-enable exit on error

    # Create comprehensive summary
    create_summary

    log_success "Organized $files_moved files into $OUTPUT_DIRECTORY"

    return 0  # Ensure function returns success
}

# Function to create comprehensive summary
create_summary() {
    log_info "Creating comprehensive summary..."

    local summary_file="$OUTPUT_DIRECTORY/summary.json"

    # Get current timestamp
    local collection_timestamp
    collection_timestamp=$(date -u +"%Y-%m-%d %H:%M:%S UTC")

    # Create summary JSON
    cat > "$summary_file" << EOF
{
  "collection_info": {
    "script_version": "$SCRIPT_VERSION",
    "collection_timestamp": "$collection_timestamp",
    "metrics_time_period": "$METRICS_TIME_PERIOD",
    "server_name": "$SERVER_NAME",
    "database_name": "$DATABASE_NAME",
    "resource_group": "$RESOURCE_GROUP_NAME",
    "subscription_id": "$AZURE_SUBSCRIPTION_ID"
  },
  "server_summary": {
    "server_name": "$SERVER_NAME",
    "database_name": "$DATABASE_NAME",
    "resource_group": "$RESOURCE_GROUP_NAME"
  },
  "file_organization": {
    "server_info_directory": "server-info/",
    "database_info_directory": "database-info/",
    "metrics_directory": "metrics/",
    "summary_file": "summary.json"
  }
}
EOF

    log_success "Summary created: $summary_file"
}

# Function to create ZIP file if requested
create_zip_file() {
    if [[ "$GENERATE_ZIP" != "true" ]]; then
        log_info "ZIP creation disabled (GENERATE_ZIP=false)"
        return 0
    fi

    if [[ -z "$OUTPUT_DIRECTORY" ]]; then
        log_warning "No output directory available for ZIP creation"
        return 1
    fi

    if [[ ! -d "$OUTPUT_DIRECTORY" ]]; then
        log_error "Output directory does not exist: $OUTPUT_DIRECTORY"
        return 1
    fi

    log_info "Creating ZIP file for directory: $OUTPUT_DIRECTORY"

    local zip_file="${OUTPUT_DIRECTORY}.zip"

    # Remove existing ZIP file if it exists
    if [[ -f "$zip_file" ]]; then
        log_info "Removing existing ZIP file: $zip_file"
        rm -f "$zip_file"
    fi

    if command -v zip &> /dev/null; then
        log_info "Using zip utility to create archive..."

        if zip -r "$zip_file" "$OUTPUT_DIRECTORY" 2>&1; then
            if [[ -f "$zip_file" ]]; then
                log_success "ZIP file created successfully: $zip_file"

                # Get ZIP file size
                local zip_size
                if command -v du &> /dev/null; then
                    zip_size=$(du -h "$zip_file" 2>/dev/null | cut -f1)
                    log_info "ZIP file size: $zip_size"
                fi

                # Get file count in ZIP
                local file_count
                if command -v unzip &> /dev/null; then
                    file_count=$(unzip -l "$zip_file" 2>/dev/null | tail -1 | awk '{print $2}')
                    log_info "Files in ZIP: $file_count"
                fi

                # Remove original directory after successful ZIP creation
                log_info "Removing original directory: $OUTPUT_DIRECTORY"
                if rm -rf "$OUTPUT_DIRECTORY"; then
                    log_success "Original directory removed successfully"
                    log_info "Final output: $zip_file"
                else
                    log_error "Failed to remove original directory: $OUTPUT_DIRECTORY"
                    log_warning "ZIP file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                log_error "ZIP file was not created successfully"
                return 1
            fi
        else
            log_error "Failed to create ZIP file using zip utility"
            return 1
        fi
    elif command -v tar &> /dev/null; then
        log_info "ZIP utility not available, using tar with gzip compression..."

        local tar_file="${OUTPUT_DIRECTORY}.tar.gz"

        if [[ -f "$tar_file" ]]; then
            log_info "Removing existing tar file: $tar_file"
            rm -f "$tar_file"
        fi

        if tar -czf "$tar_file" "$OUTPUT_DIRECTORY" 2>&1; then
            if [[ -f "$tar_file" ]]; then
                log_success "TAR.GZ file created successfully: $tar_file"

                local tar_size
                if command -v du &> /dev/null; then
                    tar_size=$(du -h "$tar_file" 2>/dev/null | cut -f1)
                    log_info "TAR.GZ file size: $tar_size"
                fi

                # Remove original directory
                log_info "Removing original directory: $OUTPUT_DIRECTORY"
                if rm -rf "$OUTPUT_DIRECTORY"; then
                    log_success "Original directory removed successfully"
                    log_info "Final output: $tar_file"
                else
                    log_error "Failed to remove original directory: $OUTPUT_DIRECTORY"
                    log_warning "TAR.GZ file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                log_error "TAR.GZ file was not created successfully"
                return 1
            fi
        else
            log_error "Failed to create TAR.GZ file"
            return 1
        fi
    else
        log_warning "Neither zip nor tar utilities are available, skipping archive creation"
        log_info "Please install zip or tar to enable archive creation"
        log_info "Data is available in directory: $OUTPUT_DIRECTORY"
        return 1
    fi
}

# Main function
main() {
    local config_file="$1"

    # Display header
    echo "=================================================="
    echo "         AZURE SQL DATABASE MONITOR"
    echo "=================================================="
    echo "Script Version: $SCRIPT_VERSION"
    echo "Execution Time: $(date)"
    echo "=================================================="

    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi

    # Validate configuration
    if ! validate_config "$config_file"; then
        exit 1
    fi

    # ZIP setting is already handled by the argument parsing and config preservation

    # Check Azure login and subscription
    if ! check_azure_login; then
        exit 1
    fi

    # Validate resource group
    if ! validate_resource_group; then
        exit 1
    fi

    # Check SQL Server and Database
    if ! check_sql_server_database; then
        exit 1
    fi

    # Main execution flow
    log_info "Starting comprehensive SQL Database analysis..."

    # Get server and database details
    if ! get_server_database_details; then
        log_warning "Failed to get server/database details, continuing with other operations..."
    fi

    # Collect metrics if enabled
    if [[ "$COLLECT_METRICS" == "true" ]]; then
        if ! collect_database_metrics_new; then
            log_warning "Failed to collect some metrics, continuing with other operations..."
        fi
    else
        log_info "Skipping metrics collection (COLLECT_METRICS=false)"
    fi

    # Collect additional database insights (temporarily disabled for testing)
    # collect_database_insights

    # Fetch database parameters if enabled
    if ! fetch_database_parameters; then
        log_warning "Failed to fetch database parameters, continuing with other operations..."
    fi

    # Organize JSON files into structured directory
    if ! organize_json_files; then
        log_warning "Failed to organize files, but continuing..."
    fi

    # Create ZIP file if requested
    if ! create_zip_file; then
        log_warning "Failed to create ZIP file, but data is still available in directory"
    fi

    echo ""
    echo "=================================================="
    echo "           COLLECTION COMPLETED"
    echo "=================================================="
    log_success "SQL Database analysis completed successfully!"

    if [[ -n "$OUTPUT_DIRECTORY" ]]; then
        log_success "All data organized in: $OUTPUT_DIRECTORY"

        # Show directory contents
        echo ""
        log_info "Generated files and directories:"
        if command -v tree &> /dev/null; then
            tree "$OUTPUT_DIRECTORY" 2>/dev/null || ls -la "$OUTPUT_DIRECTORY"
        else
            find "$OUTPUT_DIRECTORY" -type f | sort
        fi
    fi

    echo ""
    log_info "You can now analyze the collected data or import it into your preferred tools"
    echo "=================================================="

    # Explicit success exit
    return 0
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    CONFIG_FILE=""
    GENERATE_ZIP_ARG=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --config-path)
                if [[ -n "$2" && "$2" != --* ]]; then
                    CONFIG_FILE="$2"
                    shift 2
                else
                    echo "Error: --config-path requires a file path argument"
                    exit 1
                fi
                ;;
            --generate-zip)
                if [[ -n "$2" && "$2" != --* ]]; then
                    case "$2" in
                        true|false)
                            GENERATE_ZIP_ARG="$2"
                            shift 2
                            ;;
                        *)
                            echo "Error: --generate-zip requires 'true' or 'false' as argument"
                            exit 1
                            ;;
                    esac
                else
                    echo "Error: --generate-zip requires 'true' or 'false' as argument"
                    exit 1
                fi
                ;;
            --help|-h)
                echo "Azure SQL Database Monitoring Script"
                echo ""
                echo "Usage: $0 --config-path <config-file> [--generate-zip <true|false>]"
                echo ""
                echo "Required Arguments:"
                echo "  --config-path PATH    Path to configuration file (.json or .conf)"
                echo ""
                echo "Optional Arguments:"
                echo "  --generate-zip BOOL   Create ZIP archive (default: true)"
                echo "  --help, -h           Show this help message"
                echo ""
                echo "Examples:"
                echo "  # JSON configuration (recommended) - ZIP generated by default"
                echo "  $0 --config-path config.json"
                echo ""
                echo "  # Legacy .conf configuration - ZIP generated by default"
                echo "  $0 --config-path config.conf"
                echo ""
                echo "  # Disable ZIP generation (keep directory structure)"
                echo "  $0 --config-path config.json --generate-zip false"
                echo ""
                echo "JSON Configuration Format:"
                echo "  {"
                echo "    \"azure\": {"
                echo "      \"subscription_id\": \"your-subscription-id\","
                echo "      \"resource_group\": \"your-resource-group\","
                echo "      \"server_name\": \"your-sql-server\","
                echo "      \"database_name\": \"your-database\""
                echo "    },"
                echo "    \"monitoring\": {"
                echo "      \"metrics_time_period\": \"1h\""
                echo "    },"
                echo "    \"collection\": {"
                echo "      \"fetch_parameters\": true,"
                echo "      \"collect_metrics\": true,"
                echo "      \"metrics_list\": [\"cpu_percent\", \"dtu_consumption_percent\", \"storage_percent\"]"
                echo "    }"
                echo "  }"
                echo ""
                echo "Legacy .conf Configuration Format:"
                echo "  AZURE_SUBSCRIPTION_ID=\"your-subscription-id\""
                echo "  RESOURCE_GROUP_NAME=\"your-resource-group\""
                echo "  SERVER_NAME=\"your-sql-server\""
                echo "  DATABASE_NAME=\"your-database\""
                echo "  METRICS_TIME_PERIOD=\"1h\"  # Optional: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
                echo "  FETCH_PARAMETERS=\"true\"   # Optional: true/false"
                echo "  COLLECT_METRICS=\"true\"    # Optional: true/false"
                exit 0
                ;;
            *)
                echo "Error: Unknown argument '$1'"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$CONFIG_FILE" ]]; then
        echo "Error: Missing required argument --config-path"
        echo "Usage: $0 --config-path <config-file> [--generate-zip <true|false>]"
        echo "Use --help for more information"
        exit 1
    fi

    # Validate config file exists
    if [[ ! -f "$CONFIG_FILE" ]]; then
        echo "Error: Configuration file '$CONFIG_FILE' not found"
        exit 1
    fi

    # Set ZIP generation flag (default to true if not specified)
    export GENERATE_ZIP="${GENERATE_ZIP_ARG:-true}"

    # Execute main function with provided config file
    if main "$CONFIG_FILE"; then
        exit 0
    else
        exit 1
    fi
fi
