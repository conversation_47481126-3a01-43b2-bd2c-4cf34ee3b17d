# Azure SQL Database Configuration File
# Copy this file and update with your actual values

# Azure Subscription ID
AZURE_SUBSCRIPTION_ID="207a84e2-208b-4809-baab-e94a043914f5"

# Resource Group Name
RESOURCE_GROUP_NAME="amit-test"

# SQL Server Name (logical server name)
SERVER_NAME="amit-server-test"

# Database Name
DATABASE_NAME="amit-sql-database"

# Monitoring Configuration
# METRICS_TIME_PERIOD: Time period for metrics collection
# Supported values: 1h, 6h, 12h, 1d, 3d, 7d, 30d
# Examples: "1h" = 1 hour, "1d" = 1 day, "7d" = 7 days
METRICS_TIME_PERIOD="1h"

# Database Parameters Configuration
# FETCH_PARAMETERS: Whether to fetch SQL Database configuration and server settings
# Set to "true" to fetch database configuration, firewall rules, and elastic pool info
# Default: false (to keep script execution faster)
FETCH_PARAMETERS="true"

# ZIP Archive Configuration
# GENERATE_ZIP: Whether to create a ZIP archive and remove the original folder
# Set to "true" to create ZIP archive, "false" to keep folder structure
# Default: false
GENERATE_ZIP="false"
