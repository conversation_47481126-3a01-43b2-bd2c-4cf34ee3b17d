{"autoPauseDelay": 60, "availabilityZone": "NoPreference", "catalogCollation": "SQL_Latin1_General_CP1_CI_AS", "collation": "SQL_Latin1_General_CP1_CI_AS", "createMode": null, "creationDate": "2025-07-14T13:06:39.940000+00:00", "currentBackupStorageRedundancy": "Local", "currentServiceObjectiveName": "GP_S_Gen5_1", "currentSku": {"capacity": 1, "family": "Gen5", "name": "GP_S_Gen5", "size": null, "tier": "GeneralPurpose"}, "databaseId": "749605ac-bab5-40c7-97a0-01b38a329b65", "defaultSecondaryLocation": "west<PERSON>", "earliestRestoreDate": "2025-07-14T13:12:00+00:00", "edition": "GeneralPurpose", "elasticPoolId": null, "elasticPoolName": null, "encryptionProtector": null, "encryptionProtectorAutoRotation": null, "failoverGroupId": null, "federatedClientId": null, "freeLimitExhaustionBehavior": null, "highAvailabilityReplicaCount": null, "id": "/subscriptions/207a84e2-208b-4809-baab-e94a043914f5/resourceGroups/amit-test/providers/Microsoft.Sql/servers/amit-server-test/databases/amit-sql-database", "identity": null, "isInfraEncryptionEnabled": false, "keys": null, "kind": "v12.0,user,vcore,serverless", "ledgerOn": false, "licenseType": null, "location": "eastus", "longTermRetentionBackupResourceId": null, "maintenanceConfigurationId": "/subscriptions/207a84e2-208b-4809-baab-e94a043914f5/providers/Microsoft.Maintenance/publicMaintenanceConfigurations/SQL_Default", "managedBy": null, "manualCutover": null, "maxLogSizeBytes": 193273528320, "maxSizeBytes": 34359738368, "minCapacity": 0.5, "name": "amit-sql-database", "pausedDate": "2025-07-14T14:07:08.200000+00:00", "performCutover": null, "preferredEnclaveType": null, "readScale": "Disabled", "recoverableDatabaseId": null, "recoveryServicesRecoveryPointId": null, "requestedBackupStorageRedundancy": "Local", "requestedServiceObjectiveName": "GP_S_Gen5_1", "resourceGroup": "amit-test", "restorableDroppedDatabaseId": null, "restorePointInTime": null, "resumedDate": null, "sampleName": null, "secondaryType": null, "sku": {"capacity": 1, "family": "Gen5", "name": "GP_S_Gen5", "size": null, "tier": "GeneralPurpose"}, "sourceDatabaseDeletionDate": null, "sourceDatabaseId": null, "sourceResourceId": null, "status": "Paused", "tags": {}, "type": "Microsoft.Sql/servers/databases", "useFreeLimit": null, "zoneRedundant": false}