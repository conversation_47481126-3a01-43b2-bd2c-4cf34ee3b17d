{"cost": 59, "interval": "PT5M", "namespace": "Microsoft.Sql/servers/databases", "resourceregion": "eastus", "timespan": "2025-07-14T16:31:24Z/2025-07-14T17:31:24Z", "value": [{"displayDescription": "Memory usage as a percentage of the SQL DB process. Not applicable to data warehouses. (This metric is equivalent to sql_instance_memory_percent, and will be removed in the future.)", "errorCode": "Success", "id": "/subscriptions/207a84e2-208b-4809-baab-e94a043914f5/resourceGroups/amit-test/providers/Microsoft.Sql/servers/amit-server-test/databases/amit-sql-database/providers/Microsoft.Insights/metrics/sqlserver_process_memory_percent", "name": {"localizedValue": "SQL Server process memory percent", "value": "sqlserver_process_memory_percent"}, "resourceGroup": "amit-test", "timeseries": [{"data": [{"average": 21.468098958333332, "timeStamp": "2025-07-14T16:31:00Z"}, {"average": 21.484375, "timeStamp": "2025-07-14T16:36:00Z"}, {"average": 21.586371527777775, "timeStamp": "2025-07-14T16:41:00Z"}, {"average": 21.547309027777775, "timeStamp": "2025-07-14T16:46:00Z"}, {"average": 21.6015625, "timeStamp": "2025-07-14T16:51:00Z"}, {"average": 21.651475694444443, "timeStamp": "2025-07-14T16:56:00Z"}, {"average": 21.527777777777782, "timeStamp": "2025-07-14T17:01:00Z"}, {"average": 21.651475694444443, "timeStamp": "2025-07-14T17:06:00Z"}, {"average": 21.898871527777775, "timeStamp": "2025-07-14T17:11:00Z"}, {"average": 21.81640625, "timeStamp": "2025-07-14T17:16:00Z"}, {"average": 21.644965277777775, "timeStamp": "2025-07-14T17:21:00Z"}, {"average": 21.62109375, "timeStamp": "2025-07-14T17:26:00Z"}], "metadatavalues": []}], "type": "Microsoft.Insights/metrics", "unit": "Percent"}]}