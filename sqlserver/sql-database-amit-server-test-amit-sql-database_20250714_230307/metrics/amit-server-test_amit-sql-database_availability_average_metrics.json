{"cost": 59, "interval": "PT5M", "namespace": "Microsoft.Sql/servers/databases", "resourceregion": "eastus", "timespan": "2025-07-14T16:31:24Z/2025-07-14T17:31:24Z", "value": [{"displayDescription": "The percentage of SLA compliant availability for the database. Availability is calculated based on connections and for each one-minute data point the value will be either 100% if connection(s) succeed or 0% if all connections fail due to system errors. Note:Select 1-minute time granularity to view SLA compliant availability.", "errorCode": "Success", "id": "/subscriptions/207a84e2-208b-4809-baab-e94a043914f5/resourceGroups/amit-test/providers/Microsoft.Sql/servers/amit-server-test/databases/amit-sql-database/providers/Microsoft.Insights/metrics/availability", "name": {"localizedValue": "Availability", "value": "availability"}, "resourceGroup": "amit-test", "timeseries": [{"data": [{"average": 100.0, "timeStamp": "2025-07-14T16:31:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T16:36:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T16:41:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T16:46:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T16:51:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T16:56:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T17:01:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T17:06:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T17:11:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T17:16:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T17:21:00Z"}, {"average": 100.0, "timeStamp": "2025-07-14T17:26:00Z"}], "metadatavalues": []}], "type": "Microsoft.Insights/metrics", "unit": "Percent"}]}