{"administratorLogin": "CloudSAa5c3f6fb", "administratorLoginPassword": null, "administrators": {"administratorType": "ActiveDirectory", "azureAdOnlyAuthentication": true, "login": "<EMAIL>", "principalType": "User", "sid": "fce65d26-8e90-4df4-b30b-8acda53237d2", "tenantId": "f1b0170e-829e-4de0-8778-4f0de342503e"}, "externalGovernanceStatus": "Disabled", "federatedClientId": null, "fullyQualifiedDomainName": "amit-server-test.database.windows.net", "id": "/subscriptions/207a84e2-208b-4809-baab-e94a043914f5/resourceGroups/amit-test/providers/Microsoft.Sql/servers/amit-server-test", "identity": null, "isIPv6Enabled": null, "keyId": null, "kind": "v12.0", "location": "eastus", "minimalTlsVersion": "1.2", "name": "amit-server-test", "primaryUserAssignedIdentityId": null, "privateEndpointConnections": [], "publicNetworkAccess": "Disabled", "resourceGroup": "amit-test", "restrictOutboundNetworkAccess": "Disabled", "state": "Ready", "tags": {}, "type": "Microsoft.Sql/servers", "version": "12.0", "workspaceFeature": null}