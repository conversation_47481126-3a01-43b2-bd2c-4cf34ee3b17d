# Azure PostgreSQL Flexible Server Monitoring & Replica Discovery Architecture

## Overview

This system provides comprehensive monitoring, metrics collection, and replica topology discovery for Azure PostgreSQL Flexible Server instances. It automatically detects replication relationships, collects performance metrics, and organizes data in a structured format for analysis and compliance.

## System Architecture

```mermaid
graph TB
    subgraph "Input Layer"
        A[Configuration File] --> B[Script Entry Point]
        B --> C[Validation Engine]
    end
    
    subgraph "Discovery Layer"
        C --> D[Azure Authentication]
        D --> E[Resource Group Validation]
        E --> F[Server Discovery]
        F --> G[Replica Topology Discovery]
    end
    
    subgraph "Collection Layer"
        G --> H[Primary Server Metrics]
        G --> I[Replica Server Metrics]
        H --> J[Performance Data]
        I --> K[Replication Data]
        J --> L[Server Parameters]
        K --> L
    end
    
    subgraph "Organization Layer"
        L --> M[File Organization Engine]
        M --> N[Primary Server Directory]
        M --> O[Replica Node Directories]
        N --> P[Summary Generation]
        O --> P
    end
    
    subgraph "Output Layer"
        P --> Q[Structured JSON Files]
        P --> R[Metrics Reports]
        P --> S[Topology Maps]
    end
```

## PostgreSQL-Specific Features

### 1. PostgreSQL Flexible Server Metrics
Unlike MySQL, PostgreSQL Flexible Server provides these specific metrics:

#### Core Performance Metrics
- **CPU Utilization**: `cpu_percent`
- **Memory Usage**: `memory_percent`
- **Storage Metrics**: `storage_percent`, `storage_used`, `storage_free`
- **Connection Metrics**: `active_connections`, `connections_succeeded`, `connections_failed`
- **I/O Performance**: `read_iops`, `write_iops`, `read_throughput`, `write_throughput`
- **Network Traffic**: `network_bytes_ingress`, `network_bytes_egress`

#### PostgreSQL-Specific Metrics
- **Replication Lag**: `physical_replication_delay_in_seconds`
- **Transaction Management**: `maximum_used_transactionIDs`
- **Disk Performance**: `disk_queue_depth`
- **Backup Storage**: `backup_storage_used`

### 2. High Availability Detection
PostgreSQL Flexible Server supports different HA modes:
- **Disabled**: Single instance deployment
- **Zone Redundant**: Primary + Standby in different zones
- **Same Zone**: Primary + Standby in same zone

### 3. Replica Discovery Algorithm
```mermaid
flowchart TD
    A[List All PostgreSQL Servers] --> B[Analyze sourceServerResourceId]
    B --> C{Server Role Analysis}
    C -->|Primary| D[Find Read Replicas]
    C -->|Replica| E[Identify Source Server]
    C -->|Standalone| F[Single Server Mode]
    D --> G[Collect Replica Metrics]
    E --> H[Document Replica Chain]
    F --> I[Standard Collection]
    G --> J[Create Topology Map]
    H --> J
    I --> J
```

## Configuration Management

### PostgreSQL Configuration Schema
```bash
# Azure PostgreSQL Flexible Server Configuration
AZURE_SUBSCRIPTION_ID="subscription-id"
RESOURCE_GROUP_NAME="resource-group"
SERVER_NAME="postgres-server-name"

# PostgreSQL-specific settings
METRICS_TIME_PERIOD="1h"          # 1h, 6h, 12h, 1d, 3d, 7d, 30d
FETCH_PARAMETERS="true"           # Collect PostgreSQL parameters
GENERATE_ZIP="false"              # Create ZIP archive
```

### Validation Rules
- **Server Name**: Must be valid PostgreSQL Flexible Server name
- **Time Period**: PostgreSQL-compatible time ranges
- **Parameters**: PostgreSQL-specific parameter collection

## Data Models

### PostgreSQL Server Configuration Model
```json
{
  "server_summary": {
    "name": "string",
    "status": "Ready|Updating|Disabled|Dropping",
    "location": "string",
    "postgres_version": "11|12|13|14|15|16",
    "fqdn": "string",
    "availability_zone": "string",
    "pricing_tier": "Burstable|GeneralPurpose|MemoryOptimized",
    "sku_name": "string",
    "vcpu_capacity": "number",
    "storage_size_gb": "number",
    "storage_tier": "P4|P6|P10|P15|P20|P30|P40|P50",
    "storage_iops": "number",
    "backup_retention_days": "number",
    "high_availability_mode": "Disabled|ZoneRedundant|SameZone"
  }
}
```

### PostgreSQL Metrics Model
```json
{
  "performance_metrics": {
    "avg_cpu_usage_percent": "number",
    "avg_memory_usage_percent": "number",
    "avg_storage_usage_percent": "number",
    "avg_active_connections": "number",
    "avg_read_iops": "number",
    "avg_write_iops": "number",
    "avg_read_throughput": "number",
    "avg_write_throughput": "number",
    "avg_replication_lag_seconds": "number",
    "avg_transaction_ids_used": "number"
  }
}
```

## File Organization Structure

### PostgreSQL Directory Layout
```
postgres-flexible-server-{SERVER_NAME}_{TIMESTAMP}/
├── {SERVER_NAME}_replication_info.json         # Replication topology
├── summary.json                                 # Consolidated metrics
├── primary-server/                              # Primary server data
│   ├── {SERVER_NAME}_full_details.json
│   ├── {SERVER_NAME}_cpu_percent_metrics.json
│   ├── {SERVER_NAME}_memory_percent_metrics.json
│   ├── {SERVER_NAME}_storage_percent_metrics.json
│   ├── {SERVER_NAME}_storage_used_metrics.json
│   ├── {SERVER_NAME}_storage_free_metrics.json
│   ├── {SERVER_NAME}_active_connections_metrics.json
│   ├── {SERVER_NAME}_connections_succeeded_metrics.json
│   ├── {SERVER_NAME}_connections_failed_metrics.json
│   ├── {SERVER_NAME}_network_bytes_ingress_metrics.json
│   ├── {SERVER_NAME}_network_bytes_egress_metrics.json
│   ├── {SERVER_NAME}_read_iops_metrics.json
│   ├── {SERVER_NAME}_write_iops_metrics.json
│   ├── {SERVER_NAME}_read_throughput_metrics.json
│   ├── {SERVER_NAME}_write_throughput_metrics.json
│   ├── {SERVER_NAME}_physical_replication_delay_in_seconds_metrics.json
│   ├── {SERVER_NAME}_maximum_used_transactionIDs_metrics.json
│   ├── {SERVER_NAME}_disk_queue_depth_metrics.json
│   ├── {SERVER_NAME}_backup_storage_used_metrics.json
│   └── {SERVER_NAME}_parameters.json
└── replica-node-{N}/                           # Per-replica directories
    ├── {REPLICA_NAME}_replica_details.json
    ├── {REPLICA_NAME}_metrics_summary.json
    ├── {REPLICA_NAME}_cpu_metrics.json
    ├── {REPLICA_NAME}_replication_lag_metrics.json
    └── replica_info.json
```

## PostgreSQL-Specific Monitoring

### Performance Monitoring Focus Areas

#### 1. Connection Management
- **Active Connections**: Current database connections
- **Connection Success Rate**: Successful vs failed connections
- **Connection Pooling**: Efficiency metrics

#### 2. Storage Performance
- **IOPS Monitoring**: Read/Write operations per second
- **Throughput Analysis**: Data transfer rates
- **Storage Utilization**: Disk space usage patterns
- **Queue Depth**: Storage request queuing

#### 3. Replication Health
- **Physical Replication Lag**: Delay in seconds
- **Replica Synchronization**: Data consistency checks
- **Cross-Region Replication**: Geographic distribution

#### 4. Transaction Management
- **Transaction ID Usage**: PostgreSQL-specific metric
- **Vacuum Operations**: Maintenance task monitoring
- **Lock Contention**: Concurrent access issues

### High Availability Monitoring

#### Zone-Redundant HA
- **Primary-Standby Sync**: Synchronization status
- **Failover Readiness**: Standby server health
- **Zone Distribution**: Multi-zone deployment verification

#### Same-Zone HA
- **Local Redundancy**: Within-zone protection
- **Failover Time**: Recovery time objectives
- **Data Consistency**: Synchronous replication

## Integration with Azure Services

### Azure Monitor Integration
- **Custom Metrics**: PostgreSQL-specific monitoring
- **Alert Rules**: Threshold-based notifications
- **Log Analytics**: Query performance analysis
- **Application Insights**: Application-level monitoring

### Azure Backup Integration
- **Point-in-Time Recovery**: Backup verification
- **Retention Policies**: Compliance requirements
- **Cross-Region Backup**: Disaster recovery

### Azure Security Integration
- **Azure AD Authentication**: Identity management
- **Private Endpoints**: Network security
- **Encryption at Rest**: Data protection
- **SSL/TLS Configuration**: Transport security

## Troubleshooting Guide

### PostgreSQL-Specific Issues

#### Connection Problems
```bash
# Check connection limits
az postgres flexible-server parameter show \
  --resource-group "rg-name" \
  --server-name "server-name" \
  --name "max_connections"
```

#### Performance Issues
```bash
# Check slow query logs
az postgres flexible-server parameter show \
  --resource-group "rg-name" \
  --server-name "server-name" \
  --name "log_min_duration_statement"
```

#### Replication Issues
```bash
# Check replication lag
az monitor metrics list \
  --resource "/subscriptions/.../servers/server-name" \
  --metric "physical_replication_delay_in_seconds"
```

## Security Considerations

### PostgreSQL Security Features
- **Row-Level Security**: Fine-grained access control
- **SSL Enforcement**: Encrypted connections
- **Azure AD Integration**: Centralized authentication
- **Private Link**: Network isolation

### Compliance Standards
- **PCI DSS**: Payment card industry compliance
- **HIPAA**: Healthcare data protection
- **SOX**: Financial reporting requirements
- **GDPR**: European data protection

## Performance Optimization

### PostgreSQL Tuning
- **Connection Pooling**: PgBouncer integration
- **Query Optimization**: Index analysis
- **Memory Configuration**: Buffer pool tuning
- **Vacuum Strategy**: Maintenance optimization

### Azure Optimization
- **Compute Scaling**: vCore adjustment
- **Storage Scaling**: IOPS optimization
- **Network Optimization**: Bandwidth tuning
- **Geographic Distribution**: Multi-region deployment

## Future Enhancements

### Planned Features
- **Query Performance Insights**: Slow query analysis
- **Automated Tuning**: AI-driven optimization
- **Cost Optimization**: Resource right-sizing
- **Multi-Cloud Support**: Hybrid deployments

### Integration Roadmap
- **Grafana Dashboards**: Visualization integration
- **Prometheus Metrics**: Monitoring ecosystem
- **Terraform Modules**: Infrastructure as Code
- **CI/CD Integration**: Automated deployment
