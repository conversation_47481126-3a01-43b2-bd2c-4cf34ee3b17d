#!/bin/bash

# Azure PostgreSQL Flexible Server Monitoring and Replica Discovery Script
# Version: 1.0
# Description: Comprehensive monitoring, metrics collection, and replica topology discovery
#              for Azure PostgreSQL Flexible Server instances
#
# USAGE:
#   bash azure_postgres_flexible_server_check.sh --config-path <config-file> [--generate-zip <true|false>]
#
# EXAMPLES:
#   # JSON configuration (recommended) - ZIP generated by default
#   bash azure_postgres_flexible_server_check.sh --config-path config.json
#
#   # Legacy .conf configuration - ZIP generated by default
#   bash azure_postgres_flexible_server_check.sh --config-path config.conf
#
#   # Disable ZIP generation (keep directory structure)
#   bash azure_postgres_flexible_server_check.sh --config-path config.json --generate-zip false
#
# JSON CONFIGURATION FORMAT:
#   {
#     "azure": {
#       "subscription_id": "your-subscription-id",
#       "resource_group": "your-resource-group",
#       "server_name": "your-postgres-server"
#     },
#     "monitoring": {
#       "metrics_time_period": "1h"
#     },
#     "collection": {
#       "fetch_parameters": true,
#       "collect_metrics": true,
#       "metrics_list": ["cpu_percent", "memory_percent", "active_connections"]
#     }
#   }
#
# LEGACY .CONF FORMAT (still supported):
#   AZURE_SUBSCRIPTION_ID="your-subscription-id"
#   RESOURCE_GROUP_NAME="your-resource-group"
#   SERVER_NAME="your-postgres-server"
#   METRICS_TIME_PERIOD="1h"
#   FETCH_PARAMETERS="true"
#   COLLECT_METRICS="true"
#
# USAGE:
#   bash azure_postgres_flexible_server_check.sh --config-path <config-file> [--generate-zip <true|false>]
#
# EXAMPLES:
#   # JSON configuration (recommended) - ZIP generated by default
#   bash azure_postgres_flexible_server_check.sh --config-path config.json
#
#   # Legacy .conf configuration - ZIP generated by default
#   bash azure_postgres_flexible_server_check.sh --config-path config.conf
#
#   # Disable ZIP generation (keep directory structure)
#   bash azure_postgres_flexible_server_check.sh --config-path config.json --generate-zip false
#
# JSON CONFIGURATION FORMAT:
#   {
#     "azure": {
#       "subscription_id": "your-subscription-id",
#       "resource_group": "your-resource-group",
#       "server_name": "your-postgres-server"
#     },
#     "monitoring": {
#       "metrics_time_period": "1h"
#     },
#     "collection": {
#       "fetch_parameters": true,
#       "collect_metrics": true,
#       "metrics_list": ["cpu_percent", "memory_percent", "active_connections"]
#     }
#   }
#
# LEGACY .CONF FORMAT (still supported):
#   AZURE_SUBSCRIPTION_ID="your-subscription-id"
#   RESOURCE_GROUP_NAME="your-resource-group"
#   SERVER_NAME="your-postgres-server"
#   METRICS_TIME_PERIOD="1h"
#   FETCH_PARAMETERS="true"
#   COLLECT_METRICS="true"

set -uo pipefail
# Note: Removed -e to allow graceful error handling and continuation

# Global variables
OUTPUT_DIRECTORY=""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get timestamp with both local and UTC time
get_timestamp() {
    local local_time=$(date +"%Y-%m-%d %H:%M:%S")
    local utc_time=$(date -u +"%Y-%m-%d %H:%M:%S")
    echo "${local_time} (UTC: ${utc_time})"
}

# Logging functions with timestamps
log_info() {
    echo -e "${BLUE}[$(get_timestamp)] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(get_timestamp)] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(get_timestamp)] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(get_timestamp)] [ERROR]${NC} $1"
}

# Function to execute Azure CLI commands safely with error handling
safe_az_command() {
    local command_description="$1"
    shift
    local az_command=("$@")

    log_info "Executing: $command_description" >&2

    local output
    local exit_code

    # Execute the command and capture both output and exit code
    if output=$("${az_command[@]}" 2>&1); then
        exit_code=0
        echo "$output"
        return 0
    else
        exit_code=$?
        log_error "Failed: $command_description" >&2
        log_error "Command: ${az_command[*]}" >&2
        log_error "Error output: $output" >&2
        log_warning "Continuing with next operation..." >&2
        # Return empty output to avoid jq parse errors
        echo ""
        return $exit_code
    fi
}

# Function to check if required tools are available
check_prerequisites() {
    local tools=("az" "jq" "date")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Please install the missing tools and try again"
        return 1
    fi
    
    log_success "All required tools are available"
    return 0
}

# Function to load JSON configuration file
load_json_config() {
    local json_file="$1"

    # Check if jq is available
    if ! command -v jq >/dev/null 2>&1; then
        log_error "jq is required to parse JSON configuration files"
        log_info "Please install jq or use .conf format instead"
        exit 1
    fi

    # Validate JSON syntax
    if ! jq empty "$json_file" 2>/dev/null; then
        log_error "Invalid JSON syntax in config file: $json_file"
        exit 1
    fi

    # Extract configuration values from JSON
    AZURE_SUBSCRIPTION_ID=$(jq -r '.azure.subscription_id // empty' "$json_file")
    RESOURCE_GROUP_NAME=$(jq -r '.azure.resource_group // empty' "$json_file")
    SERVER_NAME=$(jq -r '.azure.server_name // empty' "$json_file")

    METRICS_TIME_PERIOD=$(jq -r '.monitoring.metrics_time_period // "1h"' "$json_file")

    FETCH_PARAMETERS=$(jq -r 'if has("collection") and (.collection | type == "object") and (.collection | has("fetch_parameters")) then (.collection.fetch_parameters | tostring) else "true" end' "$json_file")
    COLLECT_METRICS=$(jq -r 'if has("collection") and (.collection | type == "object") and (.collection | has("collect_metrics")) then (.collection.collect_metrics | tostring) else "true" end' "$json_file")

    # Convert metrics_list array to comma-separated string
    local metrics_array=$(jq -r '.collection.metrics_list[]?' "$json_file" 2>/dev/null)
    if [[ -n "$metrics_array" ]]; then
        METRICS_LIST=$(echo "$metrics_array" | tr '\n' ',' | sed 's/,$//')
    else
        # Check if metrics_list exists but is empty array
        local has_metrics_list=$(jq -r 'has("collection") and .collection | has("metrics_list")' "$json_file" 2>/dev/null)
        if [[ "$has_metrics_list" == "true" ]]; then
            METRICS_LIST=""  # Explicitly empty - don't use defaults
        else
            METRICS_LIST=""  # No metrics_list specified - will use defaults later
        fi
    fi

    log_info "Successfully loaded JSON configuration"
}

# Function to validate configuration file
validate_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "Configuration file not found: $config_file"
        return 1
    fi
    
    # Preserve command-line arguments before sourcing config file
    local preserve_generate_zip="$GENERATE_ZIP"

    # Determine config file format and load accordingly
    if [[ "$config_file" == *.json ]]; then
        load_json_config "$config_file"
    else
        # Source the config file (legacy .conf format)
        source "$config_file"
    fi

    # Restore command-line arguments if they were set
    if [[ -n "$preserve_generate_zip" ]]; then
        GENERATE_ZIP="$preserve_generate_zip"
    fi
    
    # Check required variables
    local required_vars=("AZURE_SUBSCRIPTION_ID" "RESOURCE_GROUP_NAME" "SERVER_NAME")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing required configuration variables: ${missing_vars[*]}"
        return 1
    fi
    
    # Validate time period format
    if [[ -n "${METRICS_TIME_PERIOD:-}" ]]; then
        if [[ ! "$METRICS_TIME_PERIOD" =~ ^[0-9]+[hdw]$ ]]; then
            log_error "Invalid METRICS_TIME_PERIOD format: $METRICS_TIME_PERIOD"
            log_error "Supported formats: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
            return 1
        fi
        log_success "Valid time period: $METRICS_TIME_PERIOD"
    fi
    
    # Set defaults for optional variables (but don't override command line arguments or JSON config)
    if [[ -z "${FETCH_PARAMETERS+x}" ]]; then
        FETCH_PARAMETERS="false"
    fi
    if [[ -z "${COLLECT_METRICS+x}" ]]; then
        COLLECT_METRICS="true"
    fi
    if [[ -z "${METRICS_LIST+x}" ]]; then
        METRICS_LIST="cpu_percent,memory_percent,active_connections,io_consumption_percent,network_bytes_ingress,network_bytes_egress,backup_storage_used,storage_used,storage_percent,storage_limit,storage_free,serverlog_storage_usage,serverlog_storage_percent,connections_failed,connections_succeeded,maximum_used_transactionIDs,read_replica_lag,read_throughput,write_throughput,txlogs_storage_used,pg_replica_log_delay_in_seconds,pg_replica_log_delay_in_bytes,sessions_percent,read_iops,write_iops,disk_queue_depth,oldest_backend_time_sec,deadlocks,temp_files_created,temp_bytes,wal_percentage,checkpoint_write_time,checkpoint_sync_time,shared_buffers_hit_rate,cache_hit_rate,blocked_queries,lock_timeouts,physical_replication_delay_in_seconds,logical_replication_delay_in_seconds,database_size_bytes,xact_commit,xact_rollback,blks_read,blks_hit,tup_returned,tup_fetched,tup_inserted,tup_updated,tup_deleted,conflicts,temp_files,temp_bytes_written,deadlocks_count,checkpoints_timed,checkpoints_req,checkpoint_write_time_ms,checkpoint_sync_time_ms,buffers_checkpoint,buffers_clean,maxwritten_clean,buffers_backend,buffers_backend_fsync,buffers_alloc"
    fi
    GENERATE_ZIP="${GENERATE_ZIP:-true}"
    
    log_success "Configuration validated successfully"
    return 0
}

# Function to check Azure login status
check_azure_login() {
    log_info "Checking Azure login status..."
    
    if ! az account show &>/dev/null; then
        log_error "Not logged into Azure. Please run 'az login' first"
        return 1
    fi
    
    log_success "Currently logged into Azure"
    
    # Get current subscription
    local current_subscription=$(az account show --query "id" -o tsv)
    log_info "Current subscription: $current_subscription"
    
    # Set subscription if different
    if [[ "$current_subscription" != "$AZURE_SUBSCRIPTION_ID" ]]; then
        log_info "Switching to subscription: $AZURE_SUBSCRIPTION_ID"
        if az account set --subscription "$AZURE_SUBSCRIPTION_ID"; then
            log_success "Successfully switched to subscription: $AZURE_SUBSCRIPTION_ID"
        else
            log_error "Failed to switch to subscription: $AZURE_SUBSCRIPTION_ID"
            return 1
        fi
    else
        log_success "Already using the correct subscription"
    fi
    
    return 0
}

# Function to validate resource group
validate_resource_group() {
    log_info "Validating resource group: $RESOURCE_GROUP_NAME"
    
    local rg_info=$(az group show --name "$RESOURCE_GROUP_NAME" --output json 2>/dev/null)
    
    if [[ -z "$rg_info" || "$rg_info" == "null" ]]; then
        log_error "Resource group '$RESOURCE_GROUP_NAME' not found or not accessible"
        return 1
    fi
    
    log_success "Resource group '$RESOURCE_GROUP_NAME' exists"
    
    local rg_location=$(echo "$rg_info" | jq -r '.location')
    log_info "Resource group location: $rg_location"
    
    return 0
}

# Function to check if PostgreSQL Flexible Server exists
check_postgres_server() {
    log_info "Checking PostgreSQL Flexible Server: $SERVER_NAME"
    
    local server_info=$(az postgres flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$SERVER_NAME" \
        --output json 2>/dev/null)
    
    if [[ -z "$server_info" || "$server_info" == "null" ]]; then
        log_error "PostgreSQL Flexible Server '$SERVER_NAME' not found in resource group '$RESOURCE_GROUP_NAME'"
        return 1
    fi
    
    log_success "PostgreSQL Flexible Server '$SERVER_NAME' found"
    return 0
}

# Function to convert time period to hours for date calculation
convert_time_period_to_hours() {
    local period="$1"
    local number="${period%[hdw]}"
    local unit="${period: -1}"
    
    case "$unit" in
        "h") echo "$number" ;;
        "d") echo $((number * 24)) ;;
        "w") echo $((number * 24 * 7)) ;;
        *) echo "24" ;; # Default to 24 hours
    esac
}

# Function to get comprehensive server information
get_server_details() {
    log_info "Fetching comprehensive server information..."
    
    # Get full server details
    local server_json=$(az postgres flexible-server show --resource-group "$RESOURCE_GROUP_NAME" --name "$SERVER_NAME" --output json)
    
    # Save full details to file
    echo "$server_json" > "${SERVER_NAME}_full_details.json"
    log_success "Full server details saved to: ${SERVER_NAME}_full_details.json"
    
    # Extract detailed information
    local server_state=$(echo "$server_json" | jq -r '.state // "N/A"')
    local server_version=$(echo "$server_json" | jq -r '.version // "N/A"')
    local server_location=$(echo "$server_json" | jq -r '.location // "N/A"')
    local server_fqdn=$(echo "$server_json" | jq -r '.fullyQualifiedDomainName // "N/A"')
    local availability_zone=$(echo "$server_json" | jq -r '.availabilityZone // "N/A"')
    
    # Extract SKU information and calculate vCPU
    local sku_name=$(echo "$server_json" | jq -r '.sku.name // "N/A"')
    local server_capacity="N/A"
    if [[ "$sku_name" != "N/A" ]]; then
        # Extract number from SKU name patterns like Standard_B1ms, Standard_D2ds_v4, etc.
        server_capacity=$(echo "$sku_name" | sed -E 's/.*[_]([BDEGM])([0-9]+).*/\2/' | head -1)
        # If extraction failed, try alternative pattern
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity=$(echo "$sku_name" | sed -E 's/.*([0-9]+).*/\1/' | head -1)
        fi
        # If still no valid number, set to N/A
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity="N/A"
        fi
    fi
    
    local server_tier=$(echo "$server_json" | jq -r '.sku.tier // "N/A"')
    local storage_size=$(echo "$server_json" | jq -r '.storage.storageSizeGb // "N/A"')
    local storage_tier=$(echo "$server_json" | jq -r '.storage.tier // "N/A"')
    local storage_iops=$(echo "$server_json" | jq -r '.storage.iops // "N/A"')
    local backup_retention=$(echo "$server_json" | jq -r '.backup.backupRetentionDays // "N/A"')
    local ha_mode=$(echo "$server_json" | jq -r '.highAvailability.mode // "Disabled"')
    
    echo ""
    echo "=================================================="
    echo "         POSTGRESQL FLEXIBLE SERVER DETAILS"
    echo "=================================================="
    printf "%-25s: %s\n" "Server Name" "$SERVER_NAME"
    printf "%-25s: %s\n" "Status" "$server_state"
    printf "%-25s: %s\n" "Region/Location" "$server_location"
    printf "%-25s: %s\n" "Availability Zone" "$availability_zone"
    printf "%-25s: %s\n" "PostgreSQL Version" "$server_version"
    printf "%-25s: %s\n" "FQDN" "$server_fqdn"
    echo ""
    echo "COMPUTE & INSTANCE DETAILS:"
    printf "%-25s: %s\n" "Pricing Tier" "$server_tier"
    printf "%-25s: %s\n" "SKU/Instance Type" "$sku_name"
    printf "%-25s: %s vCores\n" "vCPU Capacity" "$server_capacity"
    echo ""
    echo "STORAGE INFORMATION:"
    printf "%-25s: %s GB\n" "Storage Size" "$storage_size"
    printf "%-25s: %s\n" "Storage Tier" "$storage_tier"
    printf "%-25s: %s\n" "Storage IOPS" "$storage_iops"
    printf "%-25s: %s days\n" "Backup Retention" "$backup_retention"
    echo ""
    echo "HIGH AVAILABILITY:"
    printf "%-25s: %s\n" "HA Mode" "$ha_mode"
    
    # Determine number of instances
    if [[ "$ha_mode" == "Disabled" || "$ha_mode" == "null" ]]; then
        echo "Number of Instances: 1 (Single instance)"
    else
        echo "Number of Instances: 2 (Primary + Standby)"
    fi
    
    echo "=================================================="
}

# Function to discover and analyze replica servers
discover_replica_servers() {
    log_info "Discovering replica servers for: $SERVER_NAME"

    # Get list of all PostgreSQL flexible servers in the resource group
    local all_servers=$(az postgres flexible-server list --resource-group "$RESOURCE_GROUP_NAME" --output json 2>/dev/null)

    if [[ -z "$all_servers" || "$all_servers" == "null" ]]; then
        log_error "Failed to list servers in resource group"
        return 1
    fi

    # Find replicas of the current server
    local replicas=$(echo "$all_servers" | jq -r --arg source_server "$SERVER_NAME" '.[] | select(.sourceServerResourceId != null and (.sourceServerResourceId | contains($source_server))) | .name')

    # Also check if current server is itself a replica
    local source_server=$(echo "$all_servers" | jq -r --arg current_server "$SERVER_NAME" '.[] | select(.name == $current_server) | .sourceServerResourceId // "null"')

    echo ""
    echo "=================================================="
    echo "           REPLICATION TOPOLOGY"
    echo "=================================================="

    if [[ "$source_server" != "null" && "$source_server" != "" ]]; then
        local source_name=$(echo "$source_server" | sed 's/.*\///')
        printf "%-25s: %s (This server is a READ REPLICA)\n" "Source Server" "$source_name"
        printf "%-25s: %s\n" "Current Server" "$SERVER_NAME"

        # Save replication info
        cat > "${SERVER_NAME}_replication_info.json" << EOF
{
  "server_role": "replica",
  "server_name": "$SERVER_NAME",
  "source_server": "$source_name",
  "source_server_resource_id": "$source_server",
  "replica_servers": []
}
EOF
    else
        printf "%-25s: %s (PRIMARY/SOURCE)\n" "Current Server" "$SERVER_NAME"

        # Check if replicas exist and are not empty
        local has_replicas=false
        local replica_count=0
        local replica_array="["

        if [[ -n "$replicas" ]]; then
            # Count non-empty replica names
            while IFS= read -r replica_name; do
                if [[ -n "$replica_name" && "$replica_name" != "" ]]; then
                    has_replicas=true
                    break
                fi
            done <<< "$replicas"
        fi

        if [[ "$has_replicas" == "true" ]]; then
            echo "Read Replicas:"

            while IFS= read -r replica_name; do
                if [[ -n "$replica_name" && "$replica_name" != "" ]]; then
                    ((replica_count++))
                    printf "  %d. %s\n" "$replica_count" "$replica_name"

                    # Add to JSON array
                    if [[ "$replica_array" != "[" ]]; then
                        replica_array+=","
                    fi
                    replica_array+="\"$replica_name\""

                    # Get replica details
                    get_replica_details "$replica_name"
                fi
            done <<< "$replicas"

            replica_array+="]"

            # Save replication info for primary with replicas
            cat > "${SERVER_NAME}_replication_info.json" << EOF
{
  "server_role": "primary",
  "server_name": "$SERVER_NAME",
  "source_server": null,
  "source_server_resource_id": null,
  "replica_servers": $replica_array,
  "replica_count": $replica_count
}
EOF

            printf "%-25s: %d\n" "Total Replicas" "$replica_count"
            log_success "Found $replica_count read replica(s)"
        else
            printf "%-25s: %s\n" "Read Replicas" "None found"

            # Save replication info for standalone server
            cat > "${SERVER_NAME}_replication_info.json" << EOF
{
  "server_role": "standalone",
  "server_name": "$SERVER_NAME",
  "source_server": null,
  "source_server_resource_id": null,
  "replica_servers": [],
  "replica_count": 0
}
EOF
            log_info "This is a standalone PostgreSQL server (no replicas configured)"
        fi
    fi

    echo "=================================================="
    log_success "Replication topology saved to: ${SERVER_NAME}_replication_info.json"
}

# Function to get detailed information about a replica server
get_replica_details() {
    local replica_name="$1"

    log_info "Collecting details for replica: $replica_name"

    # Get replica server details
    local replica_details=$(az postgres flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$replica_name" \
        --output json 2>/dev/null)

    if [[ -n "$replica_details" && "$replica_details" != "null" ]]; then
        echo "$replica_details" > "${replica_name}_replica_details.json"

        # Extract key information
        local replica_state=$(echo "$replica_details" | jq -r '.state // "N/A"')
        local replica_location=$(echo "$replica_details" | jq -r '.location // "N/A"')
        local replica_sku=$(echo "$replica_details" | jq -r '.sku.name // "N/A"')

        printf "    Status: %s, Location: %s, SKU: %s\n" "$replica_state" "$replica_location" "$replica_sku"

        # Collect replica metrics if enabled
        if [[ "$COLLECT_METRICS" == "true" ]]; then
            collect_replica_metrics "$replica_name"
        fi
    else
        log_warning "Failed to get details for replica: $replica_name"
    fi
}

# Function to collect metrics from replica servers
collect_replica_metrics() {
    local replica_name="$1"

    log_info "Collecting metrics for replica: $replica_name"

    # Get resource ID for the replica
    local replica_resource_id=$(az postgres flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$replica_name" \
        --query "id" -o tsv 2>/dev/null)

    if [[ -z "$replica_resource_id" ]]; then
        log_warning "Could not get resource ID for replica: $replica_name"
        return 1
    fi

    # Calculate time range
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")

    local start_time
    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
    else
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
    fi

    # Collect key metrics for replica
    local metrics_summary="{"

    # CPU metrics
    local cpu_metrics=$(az monitor metrics list \
        --resource "$replica_resource_id" \
        --metric "cpu_percent" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    local avg_cpu="N/A"
    if [[ -n "$cpu_metrics" && "$cpu_metrics" != "null" ]]; then
        avg_cpu=$(echo "$cpu_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        echo "$cpu_metrics" > "${replica_name}_cpu_metrics.json"
    fi

    # Replication lag metrics (most important for replicas)
    local replication_lag_metrics=$(az monitor metrics list \
        --resource "$replica_resource_id" \
        --metric "physical_replication_delay_in_seconds" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval PT5M \
        --aggregation Average \
        --output json 2>/dev/null)

    local avg_replication_lag="N/A"
    if [[ -n "$replication_lag_metrics" && "$replication_lag_metrics" != "null" ]]; then
        avg_replication_lag=$(echo "$replication_lag_metrics" | jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
        echo "$replication_lag_metrics" > "${replica_name}_replication_lag_metrics.json"
    fi

    # Create replica metrics summary
    cat > "${replica_name}_metrics_summary.json" << EOF
{
  "replica_name": "$replica_name",
  "metrics_time_period": "$METRICS_TIME_PERIOD",
  "avg_cpu_usage_percent": "$avg_cpu",
  "avg_replication_lag_seconds": "$avg_replication_lag",
  "collection_timestamp": "$(date -u +"%Y-%m-%d %H:%M:%S UTC")"
}
EOF

    printf "    Metrics - CPU: %s%%, Replication Lag: %s seconds\n" "$avg_cpu" "$avg_replication_lag"
}



# Function to collect any metric
collect_metric() {
    local metric_name="$1"
    local resource_id="$2"
    local start_time="$3"
    local end_time="$4"

    # Try to collect the metric
    az monitor metrics list \
        --resource "$resource_id" \
        --metric "$metric_name" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --interval "PT5M" \
        --aggregation "Average" \
        --output json > "${SERVER_NAME}_${metric_name}_metrics.json" 2>/dev/null

    # Check if file was created and has content
    if [[ -f "${SERVER_NAME}_${metric_name}_metrics.json" && -s "${SERVER_NAME}_${metric_name}_metrics.json" ]]; then
        return 0
    else
        rm -f "${SERVER_NAME}_${metric_name}_metrics.json" 2>/dev/null
        return 1
    fi
}

# New efficient metrics collection function
collect_server_metrics_new() {
    if [[ -z "$METRICS_TIME_PERIOD" ]]; then
        log_warning "METRICS_TIME_PERIOD not set, skipping metrics collection"
        return 0
    fi

    log_info "Collecting server metrics for time period: $METRICS_TIME_PERIOD"

    # Get server resource ID
    local resource_id=$(az postgres flexible-server show \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --name "$SERVER_NAME" \
        --query "id" -o tsv)

    if [[ -z "$resource_id" ]]; then
        log_error "Could not get resource ID for server: $SERVER_NAME"
        return 1
    fi

    # Calculate time range
    local end_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local hours_ago=$(convert_time_period_to_hours "$METRICS_TIME_PERIOD")

    local start_time
    if date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null; then
        start_time=$(date -u -d "${hours_ago} hours ago" +"%Y-%m-%dT%H:%M:%SZ")
    else
        start_time=$(date -u -v-${hours_ago}H +"%Y-%m-%dT%H:%M:%SZ")
    fi

    log_info "Metrics time range: $start_time to $end_time"

    # Check if metrics list is empty
    if [[ -z "$METRICS_LIST" ]]; then
        log_info "No metrics specified in configuration, skipping metrics collection"
        return 0
    fi

    # Collect metrics from METRICS_LIST
    IFS=',' read -ra METRICS_ARRAY <<< "$METRICS_LIST"
    local metrics_collected=0
    local metrics_failed=0

    for metric_name in "${METRICS_ARRAY[@]}"; do
        metric_name=$(echo "$metric_name" | xargs)  # Trim whitespace
        [[ -z "$metric_name" ]] && continue

        log_info "Collecting metric: $metric_name"
        if collect_metric "$metric_name" "$resource_id" "$start_time" "$end_time"; then
            log_success "✓ Collected: $metric_name"
            ((metrics_collected++))
        else
            log_warning "⚠ Failed: $metric_name"
            ((metrics_failed++))
        fi
    done

    log_success "Metrics collection completed: $metrics_collected collected, $metrics_failed failed"
}

# Function to fetch PostgreSQL server parameters
fetch_server_parameters() {
    if [[ "$FETCH_PARAMETERS" != "true" ]]; then
        log_info "Skipping server parameters collection (FETCH_PARAMETERS=false)"
        return 0
    fi

    log_info "Fetching PostgreSQL server parameters..."

    local parameters=$(az postgres flexible-server parameter list \
        --resource-group "$RESOURCE_GROUP_NAME" \
        --server-name "$SERVER_NAME" \
        --output json 2>/dev/null)

    if [[ -n "$parameters" && "$parameters" != "null" ]]; then
        echo "$parameters" > "${SERVER_NAME}_parameters.json"
        log_success "Server parameters saved to: ${SERVER_NAME}_parameters.json"

        # Count non-default parameters
        local custom_params=$(echo "$parameters" | jq -r '[.[] | select(.isConfiguredByUser == true)] | length')
        log_info "Custom configured parameters: $custom_params"
    else
        log_warning "Failed to fetch server parameters"
    fi
}

# Function to organize JSON files into service directory
organize_json_files() {
    local service_dir="postgres-flexible-server-${SERVER_NAME}"
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local timestamped_dir="${service_dir}_${timestamp}"

    log_info "Organizing JSON files into directory: $timestamped_dir"

    # Create service directory
    if mkdir -p "$timestamped_dir"; then
        log_success "Created directory: $timestamped_dir"
    else
        log_error "Failed to create directory: $timestamped_dir"
        return 1
    fi

    # Create primary server subdirectory
    local primary_dir="$timestamped_dir/primary-server"
    if mkdir -p "$primary_dir"; then
        log_success "Created primary server directory: $primary_dir"
    else
        log_error "Failed to create primary server directory: $primary_dir"
        return 1
    fi

    # Move primary server JSON files to the primary-server subdirectory
    local files_moved=0

    # Move all JSON files that were actually created (dynamic discovery)
    log_info "Discovering and moving created JSON files..."

    # Find all JSON files that match the server name pattern (excluding replication info)
    local json_files_found=()
    while IFS= read -r -d '' file; do
        local filename=$(basename "$file")
        # Skip replication info file (handled separately)
        if [[ "$filename" != "${SERVER_NAME}_replication_info.json" ]]; then
            json_files_found+=("$filename")
        fi
    done < <(find . -maxdepth 1 -name "${SERVER_NAME}_*.json" -print0 2>/dev/null)

    log_info "Found ${#json_files_found[@]} JSON files to move"

    # Move discovered files (temporarily disable errexit for file operations)
    set +e  # Disable exit on error temporarily
    for json_file in "${json_files_found[@]}"; do
        if [[ -f "$json_file" ]]; then
            if mv "$json_file" "$primary_dir/" 2>/dev/null; then
                log_success "Moved primary file: $json_file -> $primary_dir/"
                ((files_moved++))
            else
                log_error "Failed to move primary file: $json_file"
                # Continue with other files instead of exiting
            fi
        fi
    done
    set -e  # Re-enable exit on error

    # Move replication info to main directory (not primary subdirectory)
    set +e  # Disable exit on error temporarily
    if [[ -f "${SERVER_NAME}_replication_info.json" ]]; then
        if mv "${SERVER_NAME}_replication_info.json" "$timestamped_dir/" 2>/dev/null; then
            log_success "Moved replication info: ${SERVER_NAME}_replication_info.json -> $timestamped_dir/"
            ((files_moved++))
        else
            log_error "Failed to move replication info file"
            # Continue instead of exiting
        fi
    fi
    set -e  # Re-enable exit on error

    # Organize replica files into numbered subdirectories
    organize_replica_files "$timestamped_dir"

    # Create summary JSON
    create_summary_json "$timestamped_dir"

    log_success "Moved $files_moved files to $timestamped_dir"

    # Set global variable for directory name (to avoid output capture issues)
    OUTPUT_DIRECTORY="$timestamped_dir"

    return 0  # Ensure function returns success
}

# Function to organize replica files into numbered subdirectories
organize_replica_files() {
    local base_dir="$1"
    local replica_count=0

    # Check if replication info exists to determine if there are replicas
    if [[ -f "$base_dir/${SERVER_NAME}_replication_info.json" ]]; then
        local replica_servers=""
        # Use safer jq command that won't fail if the structure is different
        if command -v jq >/dev/null 2>&1; then
            replica_servers=$(jq -r '.replica_servers[]?' "$base_dir/${SERVER_NAME}_replication_info.json" 2>/dev/null || echo "")
        fi

        if [[ -n "$replica_servers" ]]; then
            log_info "Organizing replica files into subdirectories..."

            while IFS= read -r replica_name; do
                if [[ -n "$replica_name" ]]; then
                    ((replica_count++))
                    local replica_dir="$base_dir/replica-node-$replica_count"

                    # Create replica subdirectory
                    if mkdir -p "$replica_dir"; then
                        log_success "Created replica directory: $replica_dir"

                        # Move replica-specific files
                        local replica_files=(
                            "${replica_name}_replica_details.json"
                            "${replica_name}_metrics_summary.json"
                            "${replica_name}_cpu_metrics.json"
                            "${replica_name}_replication_lag_metrics.json"
                        )

                        local replica_files_moved=0
                        for replica_file in "${replica_files[@]}"; do
                            if [[ -f "$replica_file" ]]; then
                                if mv "$replica_file" "$replica_dir/"; then
                                    log_success "Moved replica file: $replica_file -> $replica_dir/"
                                    ((replica_files_moved++))
                                else
                                    log_error "Failed to move replica file: $replica_file"
                                fi
                            fi
                        done

                        # Create a replica info file in the replica directory
                        cat > "$replica_dir/replica_info.json" << EOF
{
  "replica_name": "$replica_name",
  "replica_node_number": $replica_count,
  "primary_server": "$SERVER_NAME",
  "files_collected": $replica_files_moved,
  "collection_timestamp": "$(date -u +"%Y-%m-%d %H:%M:%S UTC")"
}
EOF
                        log_success "Created replica info file: $replica_dir/replica_info.json"

                    else
                        log_error "Failed to create replica directory: $replica_dir"
                    fi
                fi
            done <<< "$replica_servers"

            log_success "Organized $replica_count replica node(s) into subdirectories"
        else
            log_info "No replica servers found - creating standalone server structure"
        fi
    else
        log_info "No replication info available - assuming standalone server"
    fi
}

# Function to create comprehensive summary JSON
create_summary_json() {
    local dir="$1"

    log_info "Creating comprehensive summary..."

    # Extract key information from the server details JSON
    local server_json="$dir/primary-server/${SERVER_NAME}_full_details.json"

    if [[ ! -f "$server_json" ]]; then
        log_warning "Server details file not found, creating basic summary"
        return 1
    fi

    # Calculate average metrics from collected data
    local avg_cpu="N/A"
    local avg_memory="N/A"
    local avg_storage="N/A"
    local avg_connections="N/A"
    local avg_read_iops="N/A"
    local avg_write_iops="N/A"

    if [[ -f "$dir/primary-server/${SERVER_NAME}_cpu_percent_metrics.json" ]]; then
        avg_cpu=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_cpu_percent_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_memory_percent_metrics.json" ]]; then
        avg_memory=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_memory_percent_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_storage_percent_metrics.json" ]]; then
        avg_storage=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_storage_percent_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_active_connections_metrics.json" ]]; then
        avg_connections=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_active_connections_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_read_iops_metrics.json" ]]; then
        avg_read_iops=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_read_iops_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    if [[ -f "$dir/primary-server/${SERVER_NAME}_write_iops_metrics.json" ]]; then
        avg_write_iops=$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_write_iops_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
    fi

    # Extract vCPU from SKU name for summary
    local sku_name=$(jq -r '.sku.name' "$server_json")
    local server_capacity="N/A"
    if [[ "$sku_name" != "null" && "$sku_name" != "N/A" ]]; then
        # Extract number from SKU name patterns like Standard_B1ms, Standard_D2ds_v4, etc.
        server_capacity=$(echo "$sku_name" | sed -E 's/.*[_]([BDEGM])([0-9]+).*/\2/' | head -1)
        # If extraction failed, try alternative pattern
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity=$(echo "$sku_name" | sed -E 's/.*([0-9]+).*/\1/' | head -1)
        fi
        # If still no valid number, set to N/A
        if [[ ! "$server_capacity" =~ ^[0-9]+$ ]]; then
            server_capacity="N/A"
        fi
    fi

    # Create comprehensive summary
    cat > "$dir/summary.json" << EOF
{
  "collection_info": {
    "script_version": "1.0",
    "collection_timestamp": "$(date -u +"%Y-%m-%d %H:%M:%S UTC")",
    "metrics_time_period": "$METRICS_TIME_PERIOD",
    "server_name": "$SERVER_NAME",
    "resource_group": "$RESOURCE_GROUP_NAME",
    "subscription_id": "$AZURE_SUBSCRIPTION_ID"
  },
  "server_summary": {
    "name": $(jq '.name' "$server_json"),
    "status": $(jq '.state' "$server_json"),
    "location": $(jq '.location' "$server_json"),
    "postgres_version": $(jq '.version' "$server_json"),
    "fqdn": $(jq '.fullyQualifiedDomainName' "$server_json"),
    "availability_zone": $(jq '.availabilityZone' "$server_json"),
    "pricing_tier": $(jq '.sku.tier' "$server_json"),
    "sku_name": $(jq '.sku.name' "$server_json"),
    "vcpu_capacity": "$server_capacity",
    "storage_size_gb": $(jq '.storage.storageSizeGb' "$server_json"),
    "storage_tier": $(jq '.storage.tier' "$server_json"),
    "storage_iops": $(jq '.storage.iops' "$server_json"),
    "backup_retention_days": $(jq '.backup.backupRetentionDays' "$server_json"),
    "high_availability_mode": $(jq '.highAvailability.mode' "$server_json")
  },
  "performance_metrics": {
    "core_metrics": {
      "avg_cpu_usage_percent": "$avg_cpu",
      "avg_memory_usage_percent": "$avg_memory",
      "avg_storage_usage_percent": "$avg_storage",
      "avg_active_connections": "$avg_connections"
    },
    "io_metrics": {
      "avg_read_iops": "$avg_read_iops",
      "avg_write_iops": "$avg_write_iops",
      "avg_disk_queue_depth": "$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_disk_queue_depth_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')"
    },
    "advanced_metrics": {
      "avg_sessions_percent": "$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_sessions_percent_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')",
      "avg_deadlocks": "$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_deadlocks_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')",
      "avg_blocked_queries": "$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_blocked_queries_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')",
      "avg_cache_hit_rate": "$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_cache_hit_rate_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')",
      "avg_wal_percentage": "$(jq -r '.value[0].timeseries[0].data[] | select(.average != null) | .average' "$dir/primary-server/${SERVER_NAME}_wal_percentage_metrics.json" 2>/dev/null | awk '{sum+=$1; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')"
    }
  },
  "data_files": {
    "primary_server_directory": "primary-server/",
    "replication_info": "${SERVER_NAME}_replication_info.json",
    "primary_server_files": {
      "server_details_full": "primary-server/${SERVER_NAME}_full_details.json",
      "cpu_metrics": "primary-server/${SERVER_NAME}_cpu_percent_metrics.json",
      "memory_metrics": "primary-server/${SERVER_NAME}_memory_percent_metrics.json",
      "storage_metrics": "primary-server/${SERVER_NAME}_storage_percent_metrics.json",
      "connection_metrics": "primary-server/${SERVER_NAME}_active_connections_metrics.json",
      "read_iops_metrics": "primary-server/${SERVER_NAME}_read_iops_metrics.json",
      "write_iops_metrics": "primary-server/${SERVER_NAME}_write_iops_metrics.json",
      "replication_lag_metrics": "primary-server/${SERVER_NAME}_physical_replication_delay_in_seconds_metrics.json",
      "server_parameters": "primary-server/${SERVER_NAME}_parameters.json"
    },
    "replica_directories": "replica-node-*/",
    "note": "Replica data is organized in replica-node-N subdirectories"
  }
}
EOF

    log_success "Summary created: $dir/summary.json"
}

# Function to create ZIP file if requested
create_zip_file() {
    if [[ "$GENERATE_ZIP" != "true" ]]; then
        log_info "ZIP creation disabled (GENERATE_ZIP=false)"
        return 0
    fi

    if [[ -z "$OUTPUT_DIRECTORY" ]]; then
        log_warning "No output directory available for ZIP creation"
        return 1
    fi

    if [[ ! -d "$OUTPUT_DIRECTORY" ]]; then
        log_error "Output directory does not exist: $OUTPUT_DIRECTORY"
        return 1
    fi

    log_info "Creating ZIP file for directory: $OUTPUT_DIRECTORY"

    local zip_file="${OUTPUT_DIRECTORY}.zip"

    # Remove existing ZIP file if it exists
    if [[ -f "$zip_file" ]]; then
        log_info "Removing existing ZIP file: $zip_file"
        rm -f "$zip_file"
    fi

    if command -v zip &> /dev/null; then
        log_info "Using zip utility to create archive..."

        # Create ZIP file with verbose output for debugging
        if zip -r "$zip_file" "$OUTPUT_DIRECTORY" 2>&1; then
            if [[ -f "$zip_file" ]]; then
                log_success "ZIP file created successfully: $zip_file"

                # Get ZIP file size
                local zip_size
                if command -v du &> /dev/null; then
                    zip_size=$(du -h "$zip_file" 2>/dev/null | cut -f1)
                    log_info "ZIP file size: $zip_size"
                fi

                # Get file count in ZIP
                local file_count
                if command -v unzip &> /dev/null; then
                    file_count=$(unzip -l "$zip_file" 2>/dev/null | tail -1 | awk '{print $2}')
                    log_info "Files in ZIP: $file_count"
                fi

                # Remove original directory after successful ZIP creation
                log_info "Removing original directory: $OUTPUT_DIRECTORY"
                if rm -rf "$OUTPUT_DIRECTORY"; then
                    log_success "Original directory removed successfully"
                    log_info "Final output: $zip_file"
                else
                    log_error "Failed to remove original directory: $OUTPUT_DIRECTORY"
                    log_warning "ZIP file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                log_error "ZIP file was not created successfully"
                return 1
            fi
        else
            log_error "Failed to create ZIP file using zip utility"
            return 1
        fi
    elif command -v tar &> /dev/null; then
        log_info "ZIP utility not available, using tar with gzip compression..."

        local tar_file="${OUTPUT_DIRECTORY}.tar.gz"

        # Remove existing tar file if it exists
        if [[ -f "$tar_file" ]]; then
            log_info "Removing existing tar file: $tar_file"
            rm -f "$tar_file"
        fi

        if tar -czf "$tar_file" "$OUTPUT_DIRECTORY" 2>&1; then
            if [[ -f "$tar_file" ]]; then
                log_success "TAR.GZ file created successfully: $tar_file"

                # Get file size
                local tar_size
                if command -v du &> /dev/null; then
                    tar_size=$(du -h "$tar_file" 2>/dev/null | cut -f1)
                    log_info "TAR.GZ file size: $tar_size"
                fi

                # Remove original directory after successful TAR.GZ creation
                log_info "Removing original directory: $OUTPUT_DIRECTORY"
                if rm -rf "$OUTPUT_DIRECTORY"; then
                    log_success "Original directory removed successfully"
                    log_info "Final output: $tar_file"
                else
                    log_error "Failed to remove original directory: $OUTPUT_DIRECTORY"
                    log_warning "TAR.GZ file created but original directory still exists"
                    return 1
                fi

                return 0
            else
                log_error "TAR.GZ file was not created successfully"
                return 1
            fi
        else
            log_error "Failed to create TAR.GZ file"
            return 1
        fi
    else
        log_warning "Neither zip nor tar utilities are available, skipping archive creation"
        log_info "Please install zip or tar to enable archive creation"
        return 1
    fi
}

# Main function
main() {
    local config_file="$1"

    echo "=================================================="
    echo "    AZURE POSTGRESQL FLEXIBLE SERVER MONITOR"
    echo "=================================================="
    echo "Script Version: 1.0"
    echo "Execution Time: $(date)"
    echo "=================================================="

    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi

    # Validate configuration
    if ! validate_config "$config_file"; then
        exit 1
    fi

    # ZIP setting is already handled by the argument parsing

    # Check Azure login
    if ! check_azure_login; then
        exit 1
    fi

    # Validate resource group
    if ! validate_resource_group; then
        exit 1
    fi

    # Check if PostgreSQL server exists
    if ! check_postgres_server; then
        exit 1
    fi

    # Main execution flow
    log_info "Starting comprehensive PostgreSQL server analysis..."

    # Get server details
    get_server_details

    # Collect metrics if enabled
    if [[ "$COLLECT_METRICS" == "true" ]]; then
        collect_server_metrics_new
    else
        log_info "Skipping metrics collection (COLLECT_METRICS=false)"
    fi

    # Fetch server parameters if enabled
    fetch_server_parameters

    # Discover and analyze replica servers
    discover_replica_servers

    # Organize JSON files into service directory
    if organize_json_files; then
        log_success "File organization completed successfully"
    else
        log_warning "File organization had issues, but continuing to ZIP creation"
    fi

    # Create ZIP file if requested
    create_zip_file

    echo ""
    echo "=================================================="
    echo "           COLLECTION COMPLETED"
    echo "=================================================="
    log_success "PostgreSQL Flexible Server analysis completed successfully!"

    if [[ -n "$OUTPUT_DIRECTORY" && -d "$OUTPUT_DIRECTORY" ]]; then
        log_success "All data organized in: $OUTPUT_DIRECTORY"

        # Show directory contents
        echo ""
        log_info "Generated files and directories:"
        if command -v tree &> /dev/null; then
            tree "$OUTPUT_DIRECTORY" 2>/dev/null || ls -la "$OUTPUT_DIRECTORY"
        else
            find "$OUTPUT_DIRECTORY" -type f | sort 2>/dev/null
        fi
    fi

    echo ""
    log_info "You can now analyze the collected data or import it into your preferred tools"
    echo "=================================================="
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Parse command line arguments
    CONFIG_FILE=""
    GENERATE_ZIP_ARG=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --config-path)
                if [[ -n "$2" && "$2" != --* ]]; then
                    CONFIG_FILE="$2"
                    shift 2
                else
                    echo "Error: --config-path requires a file path argument"
                    exit 1
                fi
                ;;
            --generate-zip)
                if [[ -n "$2" && "$2" != --* ]]; then
                    case "$2" in
                        true|false)
                            GENERATE_ZIP_ARG="$2"
                            shift 2
                            ;;
                        *)
                            echo "Error: --generate-zip requires 'true' or 'false' as argument"
                            exit 1
                            ;;
                    esac
                else
                    echo "Error: --generate-zip requires 'true' or 'false' as argument"
                    exit 1
                fi
                ;;
            --help|-h)
                echo "Azure PostgreSQL Flexible Server Monitoring Script"
                echo ""
                echo "Usage: $0 --config-path <config-file> [--generate-zip <true|false>]"
                echo ""
                echo "Required Arguments:"
                echo "  --config-path PATH    Path to configuration file (.json or .conf)"
                echo ""
                echo "Optional Arguments:"
                echo "  --generate-zip BOOL   Create ZIP archive (default: true)"
                echo "  --help, -h           Show this help message"
                echo ""
                echo "Examples:"
                echo "  # JSON configuration (recommended) - ZIP generated by default"
                echo "  $0 --config-path config.json"
                echo ""
                echo "  # Legacy .conf configuration - ZIP generated by default"
                echo "  $0 --config-path config.conf"
                echo ""
                echo "  # Disable ZIP generation (keep directory structure)"
                echo "  $0 --config-path config.json --generate-zip false"
                echo ""
                echo "JSON Configuration Format:"
                echo "  {"
                echo "    \"azure\": {"
                echo "      \"subscription_id\": \"your-subscription-id\","
                echo "      \"resource_group\": \"your-resource-group\","
                echo "      \"server_name\": \"your-postgres-server\""
                echo "    },"
                echo "    \"monitoring\": {"
                echo "      \"metrics_time_period\": \"1h\""
                echo "    },"
                echo "    \"collection\": {"
                echo "      \"fetch_parameters\": true,"
                echo "      \"collect_metrics\": true,"
                echo "      \"metrics_list\": [\"cpu_percent\", \"memory_percent\", \"active_connections\"]"
                echo "    }"
                echo "  }"
                echo ""
                echo "Legacy .conf Configuration Format:"
                echo "  AZURE_SUBSCRIPTION_ID=\"your-subscription-id\""
                echo "  RESOURCE_GROUP_NAME=\"your-resource-group\""
                echo "  SERVER_NAME=\"your-postgres-server\""
                echo "  METRICS_TIME_PERIOD=\"1h\"  # Optional: 1h, 6h, 12h, 1d, 3d, 7d, 30d"
                echo "  FETCH_PARAMETERS=\"true\"   # Optional: true/false"
                echo "  COLLECT_METRICS=\"true\"    # Optional: true/false"
                exit 0
                ;;
            *)
                echo "Error: Unknown argument '$1'"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$CONFIG_FILE" ]]; then
        echo "Error: Missing required argument --config-path"
        echo "Usage: $0 --config-path <config-file> [--generate-zip <true|false>]"
        echo "Use --help for more information"
        exit 1
    fi

    # Validate config file exists
    if [[ ! -f "$CONFIG_FILE" ]]; then
        echo "Error: Configuration file '$CONFIG_FILE' not found"
        exit 1
    fi

    # Set ZIP generation flag (default to true if not specified)
    export GENERATE_ZIP="${GENERATE_ZIP_ARG:-true}"

    # Execute main function with provided config file
    main "$CONFIG_FILE"
fi
