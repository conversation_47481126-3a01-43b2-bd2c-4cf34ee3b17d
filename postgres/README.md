# Azure PostgreSQL Flexible Server Monitoring Script

Enterprise-grade monitoring script for Azure PostgreSQL Flexible Server with comprehensive metrics collection, replication analysis, and modern JSON configuration support.

## ✨ **Key Features**

- **🔧 JSON Configuration** - Modern, readable configuration format with auto-detection
- **📊 Dynamic Metrics Collection** - Configurable metrics list for future-proof monitoring
- **🕐 UTC + Local Timestamps** - Professional logging with timezone consistency
- **🔄 Replication Monitoring** - Automatic discovery and analysis of read replicas
- **📁 Organized Output** - Structured JSON files with summary reports
- **📦 ZIP Archive Support** - Compressed output for easy sharing
- **🎨 Enterprise Logging** - Color-coded output with detailed timestamps
- **🛡️ Error Resilience** - Robust error handling that continues on individual failures
- **🔄 Backward Compatibility** - Supports legacy .conf files

## 🚀 **Quick Start**

### **1. Prerequisites**
```bash
# Install Azure CLI
brew install azure-cli  # macOS
# or
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash  # Ubuntu/Debian

# Install jq for JSON processing
brew install jq  # macOS
# or  
sudo apt-get install jq  # Ubuntu/Debian

# Login to Azure
az login
```

### **2. Configuration**

#### **JSON Configuration (Recommended)**
Create a `config.json` file:
```json
{
  "azure": {
    "subscription_id": "your-subscription-id",
    "resource_group": "your-resource-group",
    "server_name": "your-postgres-server"
  },
  "monitoring": {
    "metrics_time_period": "1h",
    "_supported_values": "1h, 6h, 12h, 1d, 3d, 7d, 30d"
  },
  "collection": {
    "fetch_parameters": true,
    "collect_metrics": true,
    "metrics_list": [
      "cpu_percent",
      "memory_percent",
      "active_connections",
      "storage_used",
      "network_bytes_ingress",
      "network_bytes_egress"
    ]
  }
}
```

#### **Legacy .conf Configuration (Still Supported)**
Create a configuration file (e.g., `production.conf`):
```bash
# Azure Configuration
AZURE_SUBSCRIPTION_ID="your-subscription-id"
RESOURCE_GROUP_NAME="your-resource-group"
SERVER_NAME="your-postgres-server-name"

# Monitoring Configuration
METRICS_TIME_PERIOD="1h"  # Options: 1h, 6h, 12h, 1d, 3d, 7d, 30d

# Server Parameters Configuration
FETCH_PARAMETERS="true"   # Set to "false" to skip parameter collection
COLLECT_METRICS="true"    # Set to "false" to skip metrics collection
```

### **3. Usage**
```bash
# With JSON configuration (recommended) - ZIP generated by default
bash azure_postgres_flexible_server_check.sh --config-path config.json

# With legacy .conf configuration - ZIP generated by default
bash azure_postgres_flexible_server_check.sh --config-path production.conf

# Disable ZIP generation (keep directory structure)
bash azure_postgres_flexible_server_check.sh --config-path config.json --generate-zip false

# Get help
bash azure_postgres_flexible_server_check.sh --help
```

## 🎯 **Use Cases**

### **1. Quick Configuration Check (Fastest - ~8K ZIP)**
```json
{
  "collection": {
    "fetch_parameters": false,
    "collect_metrics": false,
    "metrics_list": []
  }
}
```

### **2. Configuration Analysis (~400K ZIP)**
```json
{
  "collection": {
    "fetch_parameters": true,
    "collect_metrics": false,
    "metrics_list": []
  }
}
```

### **3. Performance Monitoring (~20K ZIP)**
```json
{
  "collection": {
    "fetch_parameters": false,
    "collect_metrics": true,
    "metrics_list": ["cpu_percent", "memory_percent", "active_connections"]
  }
}
```

### **4. Complete Analysis (~450K ZIP)**
```json
{
  "collection": {
    "fetch_parameters": true,
    "collect_metrics": true,
    "metrics_list": [/* all available metrics */]
  }
}
```

## 📊 **Professional Logging Output**

The script provides enterprise-grade logging with UTC timestamps:

```bash
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Starting PostgreSQL Flexible Server monitoring
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [SUCCESS] All required tools are available
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Reading configuration from: production.conf
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [SUCCESS] Configuration validated successfully
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Subscription ID: 207a84e2-208b-4809-baab-e94a043914f5
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Resource Group: production-rg
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Server Name: postgres-prod-server
```

## 📁 **Output Structure**

### **Directory Organization:**
```
postgres-flexible-server-myserver_20250714_131352/
├── summary.json                           # 📋 Consolidated summary report
├── myserver_replication_info.json        # 🔗 Replication topology
└── primary-server/                        # 🖥️ Primary server data
    ├── myserver_full_details.json        # Server configuration
    ├── myserver_parameters.json          # PostgreSQL parameters
    ├── myserver_cpu_metrics.json         # CPU utilization metrics
    ├── myserver_memory_metrics.json      # Memory usage metrics
    ├── myserver_storage_metrics.json     # Storage utilization
    ├── myserver_network_in_metrics.json  # Network ingress
    ├── myserver_network_out_metrics.json # Network egress
    └── ... (15+ additional metric files)
```

### **Replica Organization (if replicas exist):**
```
postgres-flexible-server-myserver_20250714_131352/
├── summary.json
├── myserver_replication_info.json
├── primary-server/                        # Primary server data
└── replica-node-1/                       # 🔄 First replica
    ├── replica_info.json
    ├── replica1_replica_details.json
    └── replica1_metrics_summary.json
```

## 📊 **Collected Metrics**

### **Core Performance Metrics:**
- **CPU Utilization** - Average CPU usage percentage
- **Memory Utilization** - Memory consumption percentage  
- **Storage Utilization** - Storage usage percentage
- **Active Connections** - Current database connections
- **IO Consumption** - Input/output operations percentage
- **Network Traffic** - Ingress and egress bytes
- **Replication Lag** - Replica synchronization delay

### **Advanced PostgreSQL Metrics:**
- **Database Connections** - Connection pool statistics
- **Transaction Statistics** - Commits, rollbacks, deadlocks
- **Buffer Cache** - Hit ratios and efficiency metrics
- **WAL Statistics** - Write-ahead log metrics
- **Lock Statistics** - Lock waits and conflicts
- **Vacuum Statistics** - Maintenance operation metrics
- **Index Usage** - Index scan statistics

### **Configuration Data:**
- **Server Parameters** - All PostgreSQL configuration parameters
- **Server Details** - SKU, location, version, availability zone
- **High Availability** - HA configuration and status
- **Backup Settings** - Retention period and storage usage

## 🔧 **Advanced Configuration**

### **Time Period Options:**
```bash
METRICS_TIME_PERIOD="1h"   # Last 1 hour (default)
METRICS_TIME_PERIOD="6h"   # Last 6 hours  
METRICS_TIME_PERIOD="12h"  # Last 12 hours
METRICS_TIME_PERIOD="1d"   # Last 1 day
METRICS_TIME_PERIOD="3d"   # Last 3 days
METRICS_TIME_PERIOD="7d"   # Last 7 days
METRICS_TIME_PERIOD="30d"  # Last 30 days
```

### **Parameter Collection:**
```bash
FETCH_PARAMETERS="true"   # Collect all PostgreSQL parameters (recommended)
FETCH_PARAMETERS="false"  # Skip parameter collection (faster execution)
```

## 🛠️ **Troubleshooting**

### **Common Issues:**
1. **Azure CLI not found** - Install Azure CLI using prerequisites
2. **jq not found** - Install jq for JSON processing
3. **Permission denied** - Ensure proper Azure RBAC permissions
4. **Server not found** - Verify server name and resource group
5. **Subscription access** - Check subscription ID and access rights

### **Error Handling:**
The script includes robust error handling:
- **Continues on individual metric failures** - Won't stop if one metric fails
- **Graceful file operation handling** - Handles file move failures safely
- **Clear error messages** - Detailed logging for troubleshooting
- **Validation at each step** - Comprehensive input validation

## 📈 **Enterprise Benefits**

- **Global Team Coordination** - UTC timestamps for multi-timezone teams
- **Audit Compliance** - Comprehensive logging with timestamps
- **Performance Monitoring** - Historical metrics for trend analysis  
- **Capacity Planning** - Storage, CPU, and memory utilization data
- **Replication Health** - Automated replica discovery and monitoring
- **Configuration Management** - Complete parameter documentation
- **Incident Response** - Detailed system state capture

Perfect for **production environments**, **compliance audits**, and **performance optimization**! 🌟
