{"azure": {"subscription_id": "207a84e2-208b-4809-baab-e94a043914f5", "resource_group": "amit-test", "server_name": "amit-pg-server-2"}, "monitoring": {"metrics_time_period": "1h"}, "collection": {"fetch_parameters": true, "collect_metrics": true, "metrics_list": ["cpu_percent", "memory_percent", "active_connections", "io_consumption_percent", "network_bytes_ingress", "network_bytes_egress", "backup_storage_used", "storage_used", "storage_percent", "storage_limit", "storage_free", "serverlog_storage_usage", "serverlog_storage_percent", "connections_failed", "connections_succeeded", "maximum_used_transactionIDs", "read_replica_lag", "read_throughput", "write_throughput", "txlogs_storage_used", "pg_replica_log_delay_in_seconds", "pg_replica_log_delay_in_bytes", "sessions_percent", "read_iops", "write_iops", "disk_queue_depth", "oldest_backend_time_sec", "deadlocks", "temp_files_created", "temp_bytes", "wal_percentage", "checkpoint_write_time", "checkpoint_sync_time", "shared_buffers_hit_rate", "cache_hit_rate", "blocked_queries", "lock_timeouts", "physical_replication_delay_in_seconds", "logical_replication_delay_in_seconds", "database_size_bytes", "xact_commit", "xact_rollback", "blks_read", "blks_hit", "tup_returned", "tup_fetched", "tup_inserted", "tup_updated", "tup_deleted", "conflicts", "temp_files", "temp_bytes_written", "deadlocks_count", "checkpoints_timed", "checkpoints_req", "checkpoint_write_time_ms", "checkpoint_sync_time_ms", "buffers_checkpoint", "buffers_clean", "maxwritten_clean", "buffers_backend", "buffers_backend_fsync", "buffers_alloc"]}}