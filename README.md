# Azure Database Monitoring Scripts

Enterprise-grade monitoring scripts for Azure database services with comprehensive metrics collection, UTC timestamps, and professional logging.

## 🚀 **Supported Azure Services:**
- **MySQL Flexible Server** - Complete monitoring with replication topology
- **PostgreSQL Flexible Server** - Advanced metrics and configuration analysis
- **SQL Database** - Server and database-level monitoring
- **Virtual Machines** - Comprehensive VM monitoring with performance metrics

## ✨ **Key Features:**
- **UTC + Local Timestamps** - Professional logging with timezone consistency
- **Comprehensive Metrics** - CPU, memory, storage, network, and service-specific metrics
- **Multi-Service Support** - Database services (MySQL, PostgreSQL, SQL) and Virtual Machines
- **Replication Monitoring** - Automatic discovery and analysis of read replicas (databases)
- **VM Resource Analysis** - Extensions, disks, network interfaces, and performance metrics
- **Organized Output** - Structured JSON files with summary reports
- **ZIP Archive Support** - Compressed output for easy sharing
- **Enterprise Logging** - Color-coded output with detailed timestamps
- **Error Resilience** - Robust error handling that continues on individual failures

## Prerequisites

1. **Azure CLI**: Make sure Azure CLI is installed on your system
   ```bash
   # Install Azure CLI (macOS)
   brew install azure-cli
   
   # Install Azure CLI (Ubuntu/Debian)
   curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
   ```

2. **Azure Login**: You must be logged into Azure CLI
   ```bash
   az login
   ```

## Configuration

1. Copy the example configuration file:
   ```bash
   cp config.example config.conf
   ```

2. Edit `config.conf` with your actual values:
   ```bash
   # Azure Configuration File
   AZURE_SUBSCRIPTION_ID="your-actual-subscription-id"
   RESOURCE_GROUP_NAME="your-resource-group-name"
   SERVER_NAME="your-mysql-server-name"

   # Monitoring Configuration
   METRICS_TIME_PERIOD="1h"  # Options: 1h, 6h, 12h, 1d, 3d, 7d, 30d

   # Server Parameters Configuration
   FETCH_PARAMETERS="false"  # Set to "true" to fetch all MySQL parameters
   ```

## 📋 **Quick Start Guide**

### **1. Choose Your Azure Service:**

#### **MySQL Flexible Server:**
```bash
cd mysql
bash azure_mysql_flexible_server_check.sh --config-path test-config.conf --generate-zip true
```

#### **PostgreSQL Flexible Server:**
```bash
cd postgres
bash azure_postgres_flexible_server_check.sh --config-path test-config.conf --generate-zip true
```

#### **SQL Database:**
```bash
cd sqlserver
bash azure_sql_database_check.sh --config-path test-config.conf --generate-zip true
```

#### **Virtual Machines:**
```bash
cd vm
bash azure_vm_check.sh --config-path test-config.conf --generate-zip true
```

### **2. Command Syntax:**
```bash
# All scripts use consistent argument format:
bash script_name.sh --config-path <config-file> --generate-zip <true|false>

# Examples:
bash azure_mysql_flexible_server_check.sh --config-path production.conf --generate-zip false
bash azure_postgres_flexible_server_check.sh --config-path staging.conf --generate-zip true
```

### **3. Professional Logging Output:**
All scripts now include **UTC timestamps** for global team coordination:

```bash
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Starting Azure MySQL Flexible Server validation script
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [SUCCESS] All required tools are available
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [INFO] Reading configuration from: test-config.conf
[2025-07-14 13:13:52 (UTC: 2025-07-14 07:43:52)] [SUCCESS] Configuration validated successfully
```

### **4. Help and Validation:**
```bash
# Get help for any script:
bash azure_mysql_flexible_server_check.sh --help
bash azure_postgres_flexible_server_check.sh --help
bash azure_sql_database_check.sh --help

# All scripts validate arguments and provide clear error messages:
bash azure_mysql_flexible_server_check.sh                    # ❌ Missing required arguments
bash azure_mysql_flexible_server_check.sh --config-path missing.conf --generate-zip true  # ❌ File not found
```

## What the Script Does

1. **Validates Prerequisites**: Checks if Azure CLI and jq are installed
2. **Loads Configuration**: Reads and validates the config file with all parameters
3. **Checks Azure Login**: Verifies you're logged into Azure and can access the specified subscription
4. **Validates Subscription**: Ensures the subscription exists and is accessible
5. **Validates Resource Group**: Confirms the resource group exists in the subscription
6. **Collects Server Information**: Retrieves comprehensive MySQL Flexible Server details
7. **Gathers Monitoring Metrics**: Collects 11 different types of performance metrics
8. **Fetches Server Parameters**: Optionally collects all 446+ MySQL configuration parameters
9. **Organizes Output**: Creates timestamped directories with all collected data
10. **Creates ZIP Archive**: Optionally compresses output and cleans up directories

## Output

The script will:
- Display colored status messages (INFO, SUCCESS, WARNING, ERROR)
- Show detailed server information in table format
- **Organize all data into a timestamped service directory**
- Display key server properties and monitoring metrics
- **Optionally create ZIP archive for easy distribution**

### Generated Directory Structure

The script creates a timestamped directory with comprehensive JSON files:

```
mysql-flexible-server-{SERVER_NAME}_{TIMESTAMP}/
├── summary.json                              # Quick overview with key metrics
├── {SERVER_NAME}_full_details.json          # Complete server configuration
├── {SERVER_NAME}_cpu_metrics.json           # CPU utilization data
├── {SERVER_NAME}_memory_metrics.json        # Memory usage data
├── {SERVER_NAME}_storage_metrics.json       # Storage utilization data
├── {SERVER_NAME}_connection_metrics.json    # Connection metrics
├── {SERVER_NAME}_io_metrics.json           # IO performance data
├── {SERVER_NAME}_network_in_metrics.json    # Network ingress data
├── {SERVER_NAME}_network_out_metrics.json   # Network egress data
├── {SERVER_NAME}_replication_lag_metrics.json # Replication lag data
├── {SERVER_NAME}_aborted_connections_metrics.json # Connection failures
├── {SERVER_NAME}_queries_metrics.json       # Query performance data
└── {SERVER_NAME}_parameters.json           # MySQL parameters (if enabled)
```

### ZIP Archive Option

When using `--generate-zip`, the script will:
- Create a compressed ZIP file containing all data
- Remove the original directory (keeping only the ZIP)
- Achieve ~95% compression ratio
- Provide a single file for easy sharing/archival

### Summary JSON Structure

The `summary.json` file provides a quick overview:

```json
{
  "report_metadata": {
    "timestamp": "2025-07-09 13:36:40 UTC",
    "subscription_id": "207a84e2-208b-4809-baab-e94a043914f5",
    "resource_group": "amit-test",
    "server_name": "amit-mysql-server",
    "metrics_time_period": "1h",
    "service_type": "Azure Database for MySQL - Flexible Server"
  },
  "server_summary": {
    "status": "Ready",
    "location": "Central India",
    "pricing_tier": "Burstable",
    "sku_name": "Standard_B1ms",
    "storage_size_gb": 20,
    "storage_iops": 360
  },
  "monitoring_summary": {
    "avg_cpu_usage_percent": "8.20",
    "avg_memory_usage_percent": "25.08",
    "avg_storage_usage_percent": "2.93",
    "avg_active_connections": "4",
    "avg_io_consumption_percent": "0.76"
  },
  "data_files": {
    "server_details": "amit-mysql-server_full_details.json",
    "cpu_metrics": "amit-mysql-server_cpu_metrics.json",
    ...
  }
}
```

## Comprehensive Features

### 📊 **Monitoring Metrics (11 Types)**
- **CPU Utilization** - Average percentage over time period
- **Memory Usage** - Average memory consumption percentage
- **Storage Usage** - Storage utilization percentage
- **Active Connections** - Current database connections
- **IO Consumption** - Storage I/O performance percentage
- **Network Traffic** - Ingress and egress bytes
- **Replication Lag** - Replication delay (if applicable)
- **Backup Storage** - Backup storage consumption
- **Server Log Storage** - Log storage usage
- **Aborted Connections** - Connection failure count
- **Query Performance** - Total queries executed

### ⚙️ **Server Configuration**
- **Complete Server Details** - All Azure MySQL server properties
- **MySQL Parameters** - All 446+ configuration parameters (optional)
- **High Availability** - HA configuration and status
- **Storage Configuration** - Size, IOPS, tier information
- **Network Settings** - FQDN, availability zones

### 🕒 **Flexible Time Periods**
- **1h** - Last 1 hour (default)
- **6h** - Last 6 hours
- **12h** - Last 12 hours
- **1d** - Last 1 day
- **3d** - Last 3 days
- **7d** - Last 7 days
- **30d** - Last 30 days

### 📦 **Output Options**
- **Organized Directories** - Timestamped folders with all data
- **ZIP Compression** - Optional 95% compression with cleanup
- **JSON Format** - Machine-readable structured data
- **Summary Reports** - Quick overview with key metrics

### Benefits

- **Enterprise-Grade**: Comprehensive monitoring and configuration documentation
- **Organized**: All data contained in timestamped directories
- **Efficient**: Optional ZIP compression saves 95% space
- **Complete Data**: All Azure API responses preserved
- **Historical Tracking**: Timestamped runs for trend analysis
- **Flexible**: Configurable time periods and optional features
- **Portable**: JSON format works with any tool or programming language
- **Clean Workspace**: Optional ZIP mode removes clutter

## Error Handling

The script will exit with appropriate error messages if:
- Azure CLI is not installed
- Configuration file is missing or invalid
- Not logged into Azure
- Subscription is not accessible
- Resource group doesn't exist
- MySQL server is not found

## Example Output

### Standard Execution
```
[INFO] Starting Azure MySQL Flexible Server validation script
[INFO] Generate ZIP: false
[SUCCESS] All required tools are available
[INFO] Metrics Time Period: 1h
[INFO] Fetch Parameters: true
[SUCCESS] MySQL Flexible Server 'amit-mysql-server' found

==================================================
           MYSQL FLEXIBLE SERVER DETAILS
==================================================
Server Name              : amit-mysql-server
Status                   : Ready
Region/Location          : Central India
Pricing Tier             : Burstable
SKU/Instance Type        : Standard_B1ms
Storage Size             : 20 GB
Number of Instances      : 1 (Single instance)
==================================================

==================================================
           MONITORING METRICS (LAST 1 HOUR)
==================================================
Average CPU Usage        : 7.51%
Average Memory Usage     : 25.12%
Average Storage Usage    : 2.93%
Average Active Connections: 4
Total Network In         : 40,025,436 bytes
Total Network Out        : 62,467,457 bytes
Total Queries            : 2,857
==================================================

[SUCCESS] Organized 12 JSON files into: mysql-flexible-server-amit-mysql-server_20250709_173740
```

### With ZIP Generation
```
[INFO] Creating ZIP archive: mysql-flexible-server-amit-mysql-server_20250709_173740.zip
[SUCCESS] ZIP archive created: mysql-flexible-server-amit-mysql-server_20250709_173740.zip
[INFO] ZIP file size: 49K
[SUCCESS] Original directory removed successfully
[INFO] Final output: mysql-flexible-server-amit-mysql-server_20250709_173740.zip
```

## Future Enhancements

This script currently supports MySQL Flexible Server. Future versions will include:
- Azure Database for MySQL – Single Server
- Azure Database for PostgreSQL – Single Server
- SQL databases

## Troubleshooting

### Common Issues

1. **Azure CLI not found**: Install Azure CLI using the commands in Prerequisites
2. **jq not found**: Install jq for JSON processing
   ```bash
   # macOS: brew install jq
   # Ubuntu/Debian: sudo apt-get install jq
   # CentOS/RHEL: sudo yum install jq
   ```
3. **Not logged in**: Run `az login` to authenticate
4. **Subscription not found**: Check your subscription ID and ensure you have access
5. **Resource group not found**: Verify the resource group name and subscription
6. **Server not found**: Check the server name and ensure it exists in the specified resource group
7. **ZIP creation failed**: Ensure you have write permissions and sufficient disk space
8. **Parameter fetching slow**: Set `FETCH_PARAMETERS="false"` for faster execution
9. **Metrics not available**: Some metrics may not have data for the specified time period

### Performance Tips

- Use shorter time periods (1h, 6h) for faster execution
- Disable parameter fetching (`FETCH_PARAMETERS="false"`) if not needed
- Use `--generate-zip` for storage efficiency and easier file management
