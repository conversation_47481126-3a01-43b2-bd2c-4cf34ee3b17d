#!/usr/bin/env python3
"""
Simple Azure Monitoring Data Analyzer

This script processes Azure monitoring ZIP files and generates CSV reports.
"""

import json
import zipfile
import tempfile
import shutil
from pathlib import Path
import pandas as pd
import numpy as np


def analyze_mysql_zip(zip_path: str, output_csv: str):
    """Analyze MySQL monitoring ZIP file and generate CSV report."""
    
    print(f"🚀 Starting analysis of: {zip_path}")
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp(prefix="azure_analysis_")
    
    try:
        # Extract ZIP file
        print("📦 Extracting ZIP file...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # Find extracted directory
        extracted_dirs = [d for d in Path(temp_dir).iterdir() if d.is_dir()]
        if not extracted_dirs:
            raise ValueError("No directories found in ZIP file")
        
        extracted_path = extracted_dirs[0]
        print(f"✅ Extracted to: {extracted_path}")
        
        # Discover JSON files
        print("🔍 Discovering files...")
        json_files = list(extracted_path.rglob("*.json"))
        print(f"📁 Found {len(json_files)} JSON files")
        
        # Categorize files
        server_files = []
        metric_files = []
        other_files = []
        
        for json_file in json_files:
            file_name = json_file.name.lower()
            if 'server_details' in file_name or 'server_info' in file_name:
                server_files.append(json_file)
            elif any(metric in file_name for metric in ['cpu', 'memory', 'storage', 'connection', 'network', 'io']):
                metric_files.append(json_file)
            else:
                other_files.append(json_file)
        
        print(f"  🖥️  Server files: {len(server_files)}")
        print(f"  📊 Metric files: {len(metric_files)}")
        print(f"  📄 Other files: {len(other_files)}")
        
        # Process data
        report_data = []
        
        # Process server information
        if server_files:
            print("🖥️  Processing server information...")
            for server_file in server_files:
                try:
                    with open(server_file, 'r') as f:
                        server_data = json.load(f)
                    
                    if isinstance(server_data, dict):
                        server_row = {
                            'Category': 'Server Information',
                            'Metric': 'Server Details',
                            'Value': f"Name: {server_data.get('name', 'N/A')}, "
                                    f"Location: {server_data.get('location', 'N/A')}, "
                                    f"Version: {server_data.get('version', 'N/A')}, "
                                    f"State: {server_data.get('state', 'N/A')}",
                            'Average': '',
                            'Min': '',
                            'Max': '',
                            'Count': '',
                            'Unit': 'Info'
                        }
                        report_data.append(server_row)
                        break  # Only need one server info
                        
                except Exception as e:
                    print(f"⚠️  Error processing server file {server_file}: {e}")
        
        # Process metrics
        if metric_files:
            print(f"📊 Processing {len(metric_files)} metric files...")
            metrics_processed = 0
            
            for metric_file in metric_files:
                try:
                    with open(metric_file, 'r') as f:
                        metric_data = json.load(f)
                    
                    # Extract metric name from filename
                    metric_name = metric_file.stem
                    
                    # Process Azure Monitor metrics format
                    if 'value' in metric_data and isinstance(metric_data['value'], list) and len(metric_data['value']) > 0:
                        metric_info = metric_data['value'][0]
                        
                        if 'timeseries' in metric_info and len(metric_info['timeseries']) > 0:
                            timeseries = metric_info['timeseries'][0]
                            
                            if 'data' in timeseries:
                                values = []
                                
                                for point in timeseries['data']:
                                    if 'average' in point and point['average'] is not None:
                                        values.append(float(point['average']))
                                
                                if values:
                                    # Calculate statistics
                                    avg_value = np.mean(values)
                                    min_value = np.min(values)
                                    max_value = np.max(values)
                                    count = len(values)
                                    
                                    # Determine unit
                                    unit = 'Value'
                                    if 'percent' in metric_name.lower() or 'cpu' in metric_name.lower():
                                        unit = '%'
                                    elif 'bytes' in metric_name.lower() or 'storage' in metric_name.lower():
                                        unit = 'Bytes'
                                    elif 'connection' in metric_name.lower():
                                        unit = 'Count'
                                    elif 'iops' in metric_name.lower():
                                        unit = 'IOPS'
                                    elif 'throughput' in metric_name.lower():
                                        unit = 'Bytes/sec'
                                    
                                    metric_row = {
                                        'Category': 'Performance Metrics',
                                        'Metric': metric_name,
                                        'Value': f"{avg_value:.2f}",
                                        'Average': round(avg_value, 2),
                                        'Min': round(min_value, 2),
                                        'Max': round(max_value, 2),
                                        'Count': count,
                                        'Unit': unit
                                    }
                                    report_data.append(metric_row)
                                    metrics_processed += 1
                                    
                except Exception as e:
                    print(f"⚠️  Error processing metric file {metric_file}: {e}")
            
            print(f"✅ Successfully processed {metrics_processed} metrics")
        
        # Generate CSV report
        if report_data:
            print(f"📊 Generating CSV report with {len(report_data)} rows...")
            
            # Ensure output directory exists
            Path(output_csv).parent.mkdir(parents=True, exist_ok=True)
            
            # Create DataFrame and save to CSV
            df = pd.DataFrame(report_data)
            df.to_csv(output_csv, index=False)
            
            print(f"✅ CSV report saved to: {output_csv}")
            
            # Show summary
            print("\n📈 ANALYSIS SUMMARY:")
            print(f"  📄 Total rows: {len(report_data)}")
            
            server_rows = len([r for r in report_data if r['Category'] == 'Server Information'])
            metric_rows = len([r for r in report_data if r['Category'] == 'Performance Metrics'])
            
            print(f"  🖥️  Server information: {server_rows} rows")
            print(f"  📊 Performance metrics: {metric_rows} rows")
            
            if metric_rows > 0:
                # Show top 5 metrics by average value
                metrics_df = df[df['Category'] == 'Performance Metrics'].copy()
                if not metrics_df.empty:
                    metrics_df['Average'] = pd.to_numeric(metrics_df['Average'], errors='coerce')
                    top_metrics = metrics_df.nlargest(5, 'Average')[['Metric', 'Average', 'Unit']]
                    
                    print("\n🏆 TOP 5 METRICS BY AVERAGE VALUE:")
                    for _, row in top_metrics.iterrows():
                        print(f"  📊 {row['Metric']}: {row['Average']} {row['Unit']}")
            
        else:
            print("⚠️  No data found to generate report")
        
        print("\n🎉 Analysis completed successfully!")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        raise
        
    finally:
        # Cleanup
        if Path(temp_dir).exists():
            shutil.rmtree(temp_dir)
            print("🧹 Temporary files cleaned up")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 3:
        print("Usage: python simple_analyzer.py <zip_file> <output_csv>")
        print("Example: python simple_analyzer.py ../mysql/data.zip ./output/report.csv")
        sys.exit(1)
    
    zip_file = sys.argv[1]
    output_csv = sys.argv[2]
    
    analyze_mysql_zip(zip_file, output_csv)
