
import os
import subprocess
import json
import argparse
import configparser
import shutil
import datetime
import logging

class ColoredFormatter(logging.Formatter):
    """Custom formatter to add colors to log messages."""
    COLORS = {
        'WARNING': '[93m',
        'INFO': '[92m',
        'DEBUG': '[94m',
        'CRITICAL': '[91m',
        'ERROR': '[91m',
        'ENDC': '[0m',
    }

    def format(self, record):
        log_message = super().format(record)
        return f"{self.COLORS.get(record.levelname, self.COLORS['ENDC'])}{log_message}{self.COLORS['ENDC']}"

def setup_logging():
    """Sets up colored logging."""
    handler = logging.StreamHandler()
    handler.setFormatter(ColoredFormatter('%(levelname)s: %(message)s'))
    logging.basicConfig(level=logging.INFO, handlers=[handler])

def check_prerequisites():
    """Checks if required command-line tools are installed."""
    logging.info("Checking prerequisites...")
    if not shutil.which("az"):
        logging.error("Azure CLI is not installed. Please install it and log in.")
        exit(1)
    logging.info("All prerequisites are met.")

def run_command(command):
    """Runs a shell command and returns the output."""
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True, shell=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        logging.error(f"Command failed: {e.cmd}")
        logging.error(f"Stderr: {e.stderr}")
        return None

def get_config(config_file):
    """Reads and validates the configuration file."""
    logging.info(f"Reading configuration from {config_file}...")
    config = configparser.ConfigParser()
    if not os.path.exists(config_file):
        logging.error(f"Configuration file not found: {config_file}")
        exit(1)
    config.read(config_file)
    
    required_sections = ["Azure"]
    for section in required_sections:
        if section not in config:
            logging.error(f"Missing required section in config file: {section}")
            exit(1)

    required_options = ["SubscriptionId", "ResourceGroupName", "ServerName"]
    for option in required_options:
        if option not in config["Azure"]:
            logging.error(f"Missing required option in config file: {option}")
            exit(1)
            
    return config

def validate_resource_group(resource_group, subscription_id):
    """Validates the existence of the resource group."""
    logging.info(f"Validating resource group '{resource_group}'...")
    command = f"az group show --name {resource_group} --subscription {subscription_id}"
    if run_command(command) is None:
        logging.error(f"Resource group '{resource_group}' not found in subscription '{subscription_id}'.")
        exit(1)
    logging.info("Resource group validation successful.")

def get_server_details(resource_group, server_name):
    """Gets the details of the server."""
    logging.info(f"Fetching details for server: {server_name}...")
    command = f"az mysql flexible-server show --resource-group {resource_group} --name {server_name} --output json"
    server_details_json = run_command(command)
    if server_details_json:
        return json.loads(server_details_json)
    return None

def get_metrics(server_id, primary_server_dir, server_name):
    """Gets the metrics for the server."""
    logging.info("Fetching metrics...")
    metrics = [
        "cpu_percent", "memory_percent", "storage_percent", "active_connections",
        "io_consumption_percent", "network_bytes_ingress", "network_bytes_egress",
        "replication_lag", "aborted_connections", "Queries", "backup_storage_used", "serverlog_storage_used"
    ]
    for metric in metrics:
        metric_json = run_command(
            f"az monitor metrics list --resource {server_id} --metric '{metric}' --output json"
        )
        if metric_json:
            with open(os.path.join(primary_server_dir, f"{server_name}_{metric.lower()}_metrics.json"), "w") as f:
                json.dump(json.loads(metric_json), f, indent=4)
            logging.info(f"Successfully fetched metric: {metric}")

def get_server_parameters(resource_group, server_name, primary_server_dir):
    """Gets the server parameters."""
    logging.info("Fetching server parameters...")
    parameters_json = run_command(f"az mysql flexible-server parameter list -g {resource_group} -s {server_name} --output json")
    if parameters_json:
        with open(os.path.join(primary_server_dir, f"{server_name}_parameters.json"), "w") as f:
            json.dump(json.loads(parameters_json), f, indent=4)
        logging.info("Successfully fetched server parameters.")

def discover_replicas(resource_group, server_name, output_dir):
    """Discovers and processes replica servers."""
    logging.info("Discovering replicas...")
    replicas_json = run_command(f"az mysql flexible-server replica list -g {resource_group} -s {server_name} --output json")
    if replicas_json:
        replicas = json.loads(replicas_json)
        with open(os.path.join(output_dir, f"{server_name}_replication_info.json"), "w") as f:
            json.dump(replicas, f, indent=4)
        
        for i, replica in enumerate(replicas):
            replica_name = replica['name']
            replica_dir = os.path.join(output_dir, f"replica-node-{i+1}")
            os.makedirs(replica_dir, exist_ok=True)
            
            replica_details = get_server_details(resource_group, replica_name)
            if replica_details:
                with open(os.path.join(replica_dir, f"{replica_name}_replica_details.json"), "w") as f:
                    json.dump(replica_details, f, indent=4)
                
                get_metrics(replica_details['id'], replica_dir, replica_name)

        logging.info(f"Successfully discovered and processed {len(replicas)} replicas.")

def create_summary(output_dir, subscription_id, resource_group, server_name, server_details):
    """Creates a summary of the collected data."""
    logging.info("Creating summary...")
    
    monitoring_summary = {}
    primary_server_dir = os.path.join(output_dir, "primary-server")
    for metric_file in os.listdir(primary_server_dir):
        if metric_file.endswith("_metrics.json"):
            metric_name = metric_file.replace(f"{server_name}_", "").replace("_metrics.json", "")
            with open(os.path.join(primary_server_dir, metric_file)) as f:
                metric_data = json.load(f)
                if metric_data.get("value"):
                    timeseries = metric_data["value"][0].get("timeseries", [])
                    if timeseries:
                        data = timeseries[0].get("data", [])
                        if data:
                            total = sum(item.get("total", 0) or item.get("average", 0) for item in data)
                            average = total / len(data) if len(data) > 0 else 0
                            monitoring_summary[f"avg_{metric_name}"] = f"{average:.2f}"

    summary = {
        "report_metadata": {
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC"),
            "subscription_id": subscription_id,
            "resource_group": resource_group,
            "server_name": server_name,
        },
        "server_summary": {
            "status": server_details.get("state"),
            "location": server_details.get("location"),
            "pricing_tier": server_details.get("sku", {}).get("tier"),
            "sku_name": server_details.get("sku", {}).get("name"),
            "storage_size_gb": server_details.get("storage", {}).get("storageSizeGb"),
            "ha_mode": server_details.get("highAvailability", {}).get("mode"),
            "version": server_details.get("version"),
        },
        "monitoring_summary": monitoring_summary
    }
    with open(os.path.join(output_dir, "summary.json"), "w") as f:
        json.dump(summary, f, indent=4)
    logging.info("Summary created.")

def main():
    parser = argparse.ArgumentParser(description="Azure MySQL Flexible Server Validation Script")
    parser.add_argument("config", help="Path to the configuration file")
    parser.add_argument("--generate-zip", action="store_true", help="Generate a ZIP archive of the output")
    args = parser.parse_args()

    setup_logging()
    check_prerequisites()
    config = get_config(args.config)

    subscription_id = config["Azure"]["SubscriptionId"]
    resource_group = config["Azure"]["ResourceGroupName"]
    server_name = config["Azure"]["ServerName"]
    
    run_command(f"az account set --subscription {subscription_id}")
    validate_resource_group(resource_group, subscription_id)

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"mysql-flexible-server-{server_name}_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    primary_server_dir = os.path.join(output_dir, "primary-server")
    os.makedirs(primary_server_dir, exist_ok=True)

    server_details = get_server_details(resource_group, server_name)
    if not server_details:
        logging.error(f"Could not retrieve details for server {server_name}.")
        exit(1)
        
    with open(os.path.join(primary_server_dir, f"{server_name}_full_details.json"), "w") as f:
        json.dump(server_details, f, indent=4)

    get_metrics(server_details['id'], primary_server_dir, server_name)
    get_server_parameters(resource_group, server_name, primary_server_dir)
    discover_replicas(resource_group, server_name, output_dir)
    create_summary(output_dir, subscription_id, resource_group, server_name, server_details)

    if args.generate_zip:
        logging.info("Generating ZIP archive...")
        shutil.make_archive(output_dir, 'zip', output_dir)
        shutil.rmtree(output_dir)
        logging.info(f"Successfully created ZIP archive: {output_dir}.zip")

if __name__ == "__main__":
    main()
