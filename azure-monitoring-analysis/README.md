# Azure Monitoring Data Analyzer

A Python tool for analyzing Azure monitoring data collected from PostgreSQL, MySQL, SQL Server, and Virtual Machine monitoring scripts.

## Features

- **ZIP File Processing**: Automatically extracts and processes monitoring data ZIP files
- **Comprehensive Analysis**: Calculates averages, min/max values, and statistical summaries
- **CSV Report Generation**: Creates structured CSV reports for easy analysis
- **Multiple Service Support**: Works with MySQL, PostgreSQL, SQL Server, and VM data
- **Configurable Processing**: JSON-based configuration for flexible analysis options

## Installation

```bash
# Install dependencies
poetry install

# Run the analyzer
poetry run python -m azure_analysis --config config.json
```

## Usage

### Using Configuration File
```bash
poetry run python -m azure_analysis --config config.json
```

### Direct Parameters
```bash
poetry run python -m azure_analysis --input-zip data.zip --output-csv report.csv
```

## Configuration

See `config.json` for configuration options including input paths, output formats, and processing settings.

## Output

The analyzer generates:
- **CSV Report**: Structured data with metrics, averages, and summaries
- **JSON Summary**: Detailed analysis summary (optional)
- **Logs**: Comprehensive logging for debugging and monitoring
