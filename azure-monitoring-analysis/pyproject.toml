[tool.poetry]
name = "azure-monitoring-analysis"
version = "0.1.0"
description = "Azure monitoring data post-analysis tool"
authors = ["Azure Monitoring Team"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
pandas = "^2.3.1"
numpy = "^1.24.0"
matplotlib = "^3.10.3"
seaborn = "^0.13.2"
openpyxl = "^3.1.5"


[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
black = "^25.1.0"
flake8 = "^7.3.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
