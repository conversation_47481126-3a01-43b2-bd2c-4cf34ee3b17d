#!/usr/bin/env python3
"""
Azure Monitoring Data Analyzer - Enhanced Edition

Processes Azure monitoring ZIP files and generates comprehensive Excel reports
with robust error handling and configuration support.
"""

import json
import shutil
import sys
import tempfile
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from JSON file."""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        print(f"✅ Configuration loaded from {config_path}")
        return config
    except FileNotFoundError:
        print(f"❌ Configuration file not found: {config_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in configuration file: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        sys.exit(1)


def analyze_azure_zip_with_config(config: Dict[str, Any]) -> bool:
    """Analyze Azure monitoring ZIP file and generate comprehensive Excel report using config."""

    # Extract paths from config
    zip_path = config.get('analysis', {}).get('input_zip_path', '')
    output_file = config.get('analysis', {}).get('output_excel_path', './output/analysis_report.xlsx')

    if not zip_path:
        print("❌ Error: input_zip_path not specified in configuration")
        return False

    print(f"🚀 Starting comprehensive analysis of: {zip_path}")

    # Validate input
    if not Path(zip_path).exists():
        print(f"❌ Error: ZIP file not found: {zip_path}")
        return False

    # Create temporary directory
    temp_dir = tempfile.mkdtemp(prefix="azure_analysis_")

    # Initialize error tracking
    errors = []
    warnings = []

    try:
        # Extract ZIP file
        print("📦 Extracting ZIP file...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)

        # Find extracted directory
        extracted_dirs = [d for d in Path(temp_dir).iterdir() if d.is_dir()]
        if not extracted_dirs:
            raise ValueError("No directories found in ZIP file")

        extracted_path = extracted_dirs[0]
        print(f"✅ Extracted to: {extracted_path}")

        # Discover and categorize JSON files
        print("🔍 Discovering and categorizing files...")
        json_files = list(extracted_path.rglob("*.json"))
        print(f"📁 Found {len(json_files)} JSON files")

        # Categorize files
        categorized_files = {
            'server_details': [],
            'parameters': [],
            'metrics': [],
            'replication': [],
            'summary': [],
            'other': []
        }

        for json_file in json_files:
            file_name = json_file.name.lower()

            if 'server_details' in file_name or 'server_info' in file_name:
                categorized_files['server_details'].append(json_file)
            elif 'parameter' in file_name or 'config' in file_name:
                categorized_files['parameters'].append(json_file)
            elif 'replication' in file_name or 'replica' in file_name:
                categorized_files['replication'].append(json_file)
            elif 'summary' in file_name:
                categorized_files['summary'].append(json_file)
            elif any(metric in file_name for metric in ['cpu', 'memory', 'storage', 'connection', 'network', 'io']):
                categorized_files['metrics'].append(json_file)
            else:
                categorized_files['other'].append(json_file)

        # Log discovered files
        for category, files in categorized_files.items():
            if files:
                print(f"  📂 {category.title()}: {len(files)} files")

        # Process all data with error tracking
        all_data = process_all_data_with_errors(categorized_files, errors, warnings)

        # Generate Excel report with multiple sheets including errors
        generate_excel_report_with_errors(output_file, all_data, errors, warnings)

        return True

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        raise

    finally:
        # Cleanup
        if Path(temp_dir).exists():
            shutil.rmtree(temp_dir)
            print("🧹 Temporary files cleaned up")


def process_all_data_with_errors(categorized_files, errors: List[str], warnings: List[str]):
    """Process all categorized files and extract comprehensive data with error tracking."""

    all_data = {
        'server_info': {},
        'parameters': [],
        'metrics': [],
        'replica_metrics': [],
        'replication_info': {},
        'summary_info': {},
        'raw_json_data': {}  # Store all raw JSON data for additional sheets
    }

    # Process server details
    print("🖥️  Processing server information...")
    if not categorized_files['server_details']:
        warnings.append("No server details files found")
        print("  ⚠️  No server details files found")
    else:
        for server_file in categorized_files['server_details']:
            try:
                with open(server_file, 'r') as f:
                    data = json.load(f)

                if isinstance(data, dict):
                    all_data['server_info'].update({
                        'server_name': data.get('name', 'N/A'),
                        'resource_group': data.get('resourceGroup', 'N/A'),
                        'location': data.get('location', 'N/A'),
                        'version': data.get('version', 'N/A'),
                        'state': data.get('state', 'N/A'),
                        'sku_name': data.get('sku', {}).get('name', 'N/A'),
                        'sku_tier': data.get('sku', {}).get('tier', 'N/A'),
                        'storage_size_gb': data.get('storage', {}).get('storageSizeGB', 'N/A'),
                        'backup_retention_days': data.get('backup', {}).get('backupRetentionDays', 'N/A'),
                        'fqdn': data.get('fullyQualifiedDomainName', 'N/A'),
                        'administrator_login': data.get('administratorLogin', 'N/A'),
                        'ssl_enforcement': data.get('sslEnforcement', 'N/A'),
                        'public_network_access': data.get('publicNetworkAccess', 'N/A'),
                        'creation_date': data.get('earliestRestoreDate', 'N/A'),
                        'tags': str(data.get('tags', {}))
                    })
                    print(f"  ✅ Server info: {data.get('name', 'Unknown')}")
                    break  # Only need one server info

            except json.JSONDecodeError as e:
                error_msg = f"JSON decode error in server file {server_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except FileNotFoundError as e:
                error_msg = f"Server file not found {server_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except Exception as e:
                error_msg = f"Error processing server file {server_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ⚠️  {error_msg}")

    # Process parameters
    print("⚙️  Processing server parameters...")
    if not categorized_files['parameters']:
        warnings.append("No parameter files found")
        print("  ⚠️  No parameter files found")
    else:
        for param_file in categorized_files['parameters']:
            try:
                with open(param_file, 'r') as f:
                    data = json.load(f)

                if isinstance(data, list):
                    for param in data:
                        if isinstance(param, dict):
                            all_data['parameters'].append({
                                'parameter_name': param.get('name', 'N/A'),
                                'value': param.get('value', 'N/A'),
                                'default_value': param.get('defaultValue', 'N/A'),
                                'data_type': param.get('dataType', 'N/A'),
                                'allowed_values': str(param.get('allowedValues', [])),
                                'description': param.get('description', 'N/A'),
                                'is_configurable': param.get('isConfigurable', 'N/A'),
                                'is_dynamic': param.get('isDynamic', 'N/A'),
                                'source': param.get('source', 'N/A')
                            })
                elif isinstance(data, dict) and 'value' in data:
                    # Handle different parameter format
                    for param in data.get('value', []):
                        if isinstance(param, dict):
                            all_data['parameters'].append({
                                'parameter_name': param.get('name', 'N/A'),
                                'value': param.get('properties', {}).get('value', 'N/A'),
                                'default_value': param.get('properties', {}).get('defaultValue', 'N/A'),
                                'data_type': param.get('properties', {}).get('dataType', 'N/A'),
                                'allowed_values': str(param.get('properties', {}).get('allowedValues', [])),
                                'description': param.get('properties', {}).get('description', 'N/A'),
                                'is_configurable': param.get('properties', {}).get('isConfigurable', 'N/A'),
                                'is_dynamic': param.get('properties', {}).get('isDynamic', 'N/A'),
                                'source': param.get('properties', {}).get('source', 'N/A')
                            })

            except json.JSONDecodeError as e:
                error_msg = f"JSON decode error in parameter file {param_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except FileNotFoundError as e:
                error_msg = f"Parameter file not found {param_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except Exception as e:
                error_msg = f"Error processing parameter file {param_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ⚠️  {error_msg}")

    print(f"  ✅ Processed {len(all_data['parameters'])} parameters")

    # Process replication info and replica metrics
    print("🔄 Processing replication information...")
    replica_metrics = []
    replication_config = {}

    if not categorized_files['replication']:
        warnings.append("No replication files found - replication may not be configured")
        print("  ⚠️  No replication files found - replication may not be configured")
    else:
        for repl_file in categorized_files['replication']:
            try:
                with open(repl_file, 'r') as f:
                    data = json.load(f)

                if isinstance(data, dict):
                    file_name = repl_file.name.lower()

                    # Check if this is a replica metric file (process as metric)
                    if 'replica' in file_name and 'metrics' in file_name:
                        # Process as metric for replica metrics table
                        metric_data = process_single_metric_file(repl_file, data, errors, warnings)
                        if metric_data:
                            metric_data['source'] = 'replica'
                            replica_metrics.append(metric_data)
                        print(f"  📊 Replica metric: {repl_file.name}")

                    # Check if this is actual replication configuration (not metrics)
                    elif any(keyword in file_name for keyword in ['replication_info', 'replica_info']):
                        # This is actual replication configuration
                        if 'replication_info' in file_name:
                            # Main replication configuration - handle actual structure
                            replication_config.update({
                                'server_name': data.get('server_name', 'N/A'),
                                'resource_group': data.get('resource_group', 'N/A'),
                                'server_role': data.get('server_role', 'N/A'),
                                'replica_count': data.get('replica_count', 'N/A'),
                                'total_servers_in_group': data.get('total_servers_in_group', 'N/A'),
                                'replica_servers': data.get('replica_servers', [])
                            })

                            # Format replica servers list for display
                            if isinstance(data.get('replica_servers'), list) and data['replica_servers']:
                                replica_list = []
                                for replica in data['replica_servers']:
                                    if isinstance(replica, dict):
                                        replica_name = replica.get('name', replica.get('server_name', 'Unknown'))
                                        replica_list.append(replica_name)
                                    else:
                                        replica_list.append(str(replica))
                                replication_config['replica_servers_list'] = ', '.join(replica_list)
                            else:
                                replication_config['replica_servers_list'] = 'None'

                        elif 'replica_info' in file_name:
                            # Additional replica information
                            replication_config.update({
                                'additional_replica_info': 'Available in Raw JSON Data'
                            })

                    # Handle replication lag as a separate case
                    elif 'replication_lag' in file_name and 'metrics' in file_name:
                        # This is replication lag metrics - process separately
                        if 'value' in data and data['value']:
                            lag_data = data['value'][0] if isinstance(data['value'], list) else data['value']
                            if 'timeseries' in lag_data and lag_data['timeseries']:
                                timeseries = lag_data['timeseries'][0]
                                if 'data' in timeseries and timeseries['data']:
                                    lag_values = [point.get('average', 0) for point in timeseries['data'] if point.get('average') is not None]
                                    if lag_values:
                                        replication_config.update({
                                            'avg_replication_lag_seconds': round(np.mean(lag_values), 2),
                                            'max_replication_lag_seconds': round(np.max(lag_values), 2),
                                            'min_replication_lag_seconds': round(np.min(lag_values), 2),
                                            'replication_lag_data_points': len(lag_values)
                                        })

                        print(f"  ✅ Replication config: {repl_file.name}")
                    else:
                        # Other replication-related files (skip metrics, keep config-like data)
                        if not ('metrics' in file_name):
                            file_key = repl_file.stem.replace('_metrics', '').replace('-metrics', '')
                            replication_config[f"other_{file_key}"] = "See Raw JSON Data sheets for details"
                            print(f"  📄 Other replication file: {repl_file.name}")

            except json.JSONDecodeError as e:
                error_msg = f"JSON decode error in replication file {repl_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except FileNotFoundError as e:
                error_msg = f"Replication file not found {repl_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except Exception as e:
                error_msg = f"Error processing replication file {repl_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ⚠️  {error_msg}")

    # Store processed data
    all_data['replica_metrics'] = replica_metrics
    all_data['replication_info'] = replication_config
    print(f"  ✅ Processed {len(replica_metrics)} replica metrics")
    print(f"  ✅ Processed replication configuration with {len(replication_config)} properties")

    # Process metrics
    print("📊 Processing performance metrics...")
    metrics_processed = 0

    if not categorized_files['metrics']:
        warnings.append("No metrics files found")
        print("  ⚠️  No metrics files found")
    else:
        for metric_file in categorized_files['metrics']:
            try:
                with open(metric_file, 'r') as f:
                    data = json.load(f)

                metric_name = metric_file.stem

                # Process Azure Monitor metrics format
                if isinstance(data, dict) and 'value' in data:
                    if isinstance(data['value'], list) and len(data['value']) > 0:
                        metric_info = data['value'][0]

                        if isinstance(metric_info, dict) and 'timeseries' in metric_info:
                            if isinstance(metric_info['timeseries'], list) and len(metric_info['timeseries']) > 0:
                                timeseries = metric_info['timeseries'][0]

                                if isinstance(timeseries, dict) and 'data' in timeseries:
                                    values = []
                                    timestamps = []

                                    for point in timeseries['data']:
                                        if isinstance(point, dict) and 'average' in point:
                                            if point['average'] is not None:
                                                try:
                                                    values.append(float(point['average']))
                                                    timestamps.append(point.get('timeStamp', ''))
                                                except (ValueError, TypeError):
                                                    continue

                                    if values:
                                        # Calculate statistics
                                        avg_value = np.mean(values)
                                        min_value = np.min(values)
                                        max_value = np.max(values)
                                        std_value = np.std(values)
                                        count = len(values)

                                        # Determine unit based on metric name
                                        unit = determine_metric_unit(metric_name)

                                        all_data['metrics'].append({
                                            'metric_name': metric_name,
                                            'average': round(avg_value, 2),
                                            'minimum': round(min_value, 2),
                                            'maximum': round(max_value, 2),
                                            'standard_deviation': round(std_value, 2),
                                            'data_points': count,
                                            'unit': unit,
                                            'first_timestamp': timestamps[0] if timestamps else 'N/A',
                                            'last_timestamp': timestamps[-1] if timestamps else 'N/A'
                                        })
                                        metrics_processed += 1
                                    else:
                                        warning_msg = f"No valid data points found in metric file {metric_file.name}"
                                        warnings.append(warning_msg)
                                        print(f"  ⚠️  {warning_msg}")
                                else:
                                    warning_msg = f"No data section found in metric file {metric_file.name}"
                                    warnings.append(warning_msg)
                                    print(f"  ⚠️  {warning_msg}")
                            else:
                                warning_msg = f"No timeseries data found in metric file {metric_file.name}"
                                warnings.append(warning_msg)
                                print(f"  ⚠️  {warning_msg}")
                        else:
                            warning_msg = f"Invalid timeseries format in metric file {metric_file.name}"
                            warnings.append(warning_msg)
                            print(f"  ⚠️  {warning_msg}")
                    else:
                        warning_msg = f"No value data found in metric file {metric_file.name}"
                        warnings.append(warning_msg)
                        print(f"  ⚠️  {warning_msg}")
                else:
                    warning_msg = f"Invalid metric format in file {metric_file.name}"
                    warnings.append(warning_msg)
                    print(f"  ⚠️  {warning_msg}")

            except json.JSONDecodeError as e:
                error_msg = f"JSON decode error in metric file {metric_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except FileNotFoundError as e:
                error_msg = f"Metric file not found {metric_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            except Exception as e:
                error_msg = f"Error processing metric file {metric_file.name}: {e}"
                errors.append(error_msg)
                print(f"  ⚠️  {error_msg}")

    print(f"  ✅ Processed {metrics_processed} metrics")

    # Collect all raw JSON data for additional sheets
    print("📄 Collecting raw JSON data for additional sheets...")
    all_data['raw_json_data'] = collect_raw_json_data(categorized_files, errors, warnings)

    return all_data


def process_single_metric_file(metric_file, data, errors: List[str], warnings: List[str]) -> Optional[Dict[str, Any]]:
    """Process a single metric file and return metric data."""
    try:
        metric_name = metric_file.stem

        # Process Azure Monitor metrics format
        if isinstance(data, dict) and 'value' in data:
            if isinstance(data['value'], list) and len(data['value']) > 0:
                metric_info = data['value'][0]

                if isinstance(metric_info, dict) and 'timeseries' in metric_info:
                    if isinstance(metric_info['timeseries'], list) and len(metric_info['timeseries']) > 0:
                        timeseries = metric_info['timeseries'][0]

                        if isinstance(timeseries, dict) and 'data' in timeseries:
                            values = []
                            timestamps = []

                            for point in timeseries['data']:
                                if isinstance(point, dict) and 'average' in point:
                                    if point['average'] is not None:
                                        try:
                                            values.append(float(point['average']))
                                            timestamps.append(point.get('timeStamp', ''))
                                        except (ValueError, TypeError):
                                            continue

                            if values:
                                # Calculate statistics
                                avg_value = np.mean(values)
                                min_value = np.min(values)
                                max_value = np.max(values)
                                std_value = np.std(values)
                                count = len(values)

                                # Determine unit based on metric name
                                unit = determine_metric_unit(metric_name)

                                return {
                                    'metric_name': metric_name,
                                    'average': round(avg_value, 2),
                                    'minimum': round(min_value, 2),
                                    'maximum': round(max_value, 2),
                                    'standard_deviation': round(std_value, 2),
                                    'data_points': count,
                                    'unit': unit,
                                    'first_timestamp': timestamps[0] if timestamps else 'N/A',
                                    'last_timestamp': timestamps[-1] if timestamps else 'N/A'
                                }
        return None
    except Exception as e:
        error_msg = f"Error processing metric file {metric_file.name}: {e}"
        errors.append(error_msg)
        return None


def collect_raw_json_data(categorized_files, errors: List[str], warnings: List[str]) -> Dict[str, List[Dict]]:
    """Collect all raw JSON data organized by category for additional Excel sheets."""
    raw_data = {
        'server_details_raw': [],
        'parameters_raw': [],
        'metrics_raw': [],
        'replication_raw': [],
        'summary_raw': [],
        'other_raw': []
    }

    for category, files in categorized_files.items():
        raw_key = f"{category}_raw"
        if raw_key not in raw_data:
            raw_data[raw_key] = []

        for file_path in files:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                # Flatten JSON data for Excel display
                flattened_data = flatten_json_for_excel(data, file_path.name)
                raw_data[raw_key].extend(flattened_data)

            except json.JSONDecodeError as e:
                error_msg = f"JSON decode error in raw data file {file_path.name}: {e}"
                errors.append(error_msg)
            except Exception as e:
                error_msg = f"Error reading raw data file {file_path.name}: {e}"
                errors.append(error_msg)

    return raw_data


def flatten_json_for_excel(data, filename: str, parent_key: str = '') -> List[Dict[str, Any]]:
    """Flatten JSON data into rows suitable for Excel display."""
    rows = []

    def flatten_recursive(obj, prefix=''):
        if isinstance(obj, dict):
            for key, value in obj.items():
                new_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, (dict, list)):
                    flatten_recursive(value, new_key)
                else:
                    rows.append({
                        'file_name': filename,
                        'property_path': new_key,
                        'value': str(value),
                        'data_type': type(value).__name__
                    })
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                new_key = f"{prefix}[{i}]"
                if isinstance(item, (dict, list)):
                    flatten_recursive(item, new_key)
                else:
                    rows.append({
                        'file_name': filename,
                        'property_path': new_key,
                        'value': str(item),
                        'data_type': type(item).__name__
                    })
        else:
            rows.append({
                'file_name': filename,
                'property_path': parent_key,
                'value': str(obj),
                'data_type': type(obj).__name__
            })

    flatten_recursive(data, parent_key)
    return rows


def determine_metric_unit(metric_name: str) -> str:
    """Determine the appropriate unit for a metric based on its name."""
    metric_name_lower = metric_name.lower()

    if 'percent' in metric_name_lower or 'cpu' in metric_name_lower:
        return '%'
    elif 'bytes' in metric_name_lower or 'storage' in metric_name_lower:
        return 'Bytes'
    elif 'connection' in metric_name_lower:
        return 'Count'
    elif 'iops' in metric_name_lower:
        return 'IOPS'
    elif 'throughput' in metric_name_lower:
        return 'Bytes/sec'
    elif 'time' in metric_name_lower or 'delay' in metric_name_lower:
        return 'Seconds'
    elif 'transaction' in metric_name_lower:
        return 'Count'
    else:
        return 'Value'


def generate_excel_report_with_errors(output_file: str, all_data: dict, errors: List[str], warnings: List[str]):
    """Generate comprehensive Excel report with multiple sheets including errors and warnings."""

    print(f"📊 Generating comprehensive Excel report: {output_file}")

    # Ensure output directory exists
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)

    # Create Excel writer
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:

        # Sheet 1: Summary
        print("  📋 Creating Summary sheet...")
        summary_data = []

        # Analysis metadata
        summary_data.append(['Analysis Date', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
        summary_data.append(['Server Name', all_data['server_info'].get('server_name', 'N/A')])
        summary_data.append(['Location', all_data['server_info'].get('location', 'N/A')])
        summary_data.append(['Version', all_data['server_info'].get('version', 'N/A')])
        summary_data.append(['State', all_data['server_info'].get('state', 'N/A')])
        summary_data.append(['SKU', all_data['server_info'].get('sku_name', 'N/A')])
        summary_data.append(['Storage Size (GB)', all_data['server_info'].get('storage_size_gb', 'N/A')])
        summary_data.append(['', ''])  # Empty row
        summary_data.append(['Data Summary', ''])
        summary_data.append(['Total Parameters', len(all_data['parameters'])])
        summary_data.append(['Total Metrics', len(all_data['metrics'])])
        summary_data.append(['Total Replica Metrics', len(all_data['replica_metrics'])])
        summary_data.append(['Replication Configured', 'Yes' if all_data['replication_info'] else 'No'])
        summary_data.append(['Processing Errors', len(errors)])
        summary_data.append(['Processing Warnings', len(warnings)])
        summary_data.append(['Raw JSON Files Processed', sum(len(files) for files in all_data['raw_json_data'].values())])

        # Top 5 metrics by value
        if all_data['metrics']:
            summary_data.append(['', ''])  # Empty row
            summary_data.append(['Top 5 Metrics by Average Value', ''])

            # Sort metrics by average value
            sorted_metrics = sorted(all_data['metrics'], key=lambda x: x['average'], reverse=True)[:5]
            for i, metric in enumerate(sorted_metrics, 1):
                summary_data.append([f"{i}. {metric['metric_name']}", f"{metric['average']} {metric['unit']}"])

        summary_df = pd.DataFrame(summary_data, columns=['Property', 'Value'])
        summary_df.to_excel(writer, sheet_name='Summary', index=False)

        # Sheet 2: Server Information
        print("  🖥️  Creating Server Information sheet...")
        server_data = []
        for key, value in all_data['server_info'].items():
            server_data.append([key.replace('_', ' ').title(), value])

        server_df = pd.DataFrame(server_data, columns=['Property', 'Value'])
        server_df.to_excel(writer, sheet_name='Server Information', index=False)

        # Sheet 3: Parameters
        print("  ⚙️  Creating Parameters sheet...")
        if all_data['parameters']:
            params_df = pd.DataFrame(all_data['parameters'])
            params_df.to_excel(writer, sheet_name='Parameters', index=False)
        else:
            # Create empty sheet with headers
            empty_params = pd.DataFrame(columns=['parameter_name', 'value', 'default_value', 'data_type', 'description'])
            empty_params.to_excel(writer, sheet_name='Parameters', index=False)

        # Sheet 4: Metrics
        print("  📊 Creating Metrics sheet...")
        if all_data['metrics']:
            metrics_df = pd.DataFrame(all_data['metrics'])
            metrics_df.to_excel(writer, sheet_name='Metrics', index=False)
        else:
            # Create empty sheet with headers
            empty_metrics = pd.DataFrame(columns=['metric_name', 'average', 'minimum', 'maximum', 'unit'])
            empty_metrics.to_excel(writer, sheet_name='Metrics', index=False)

        # Sheet 5: Replica Metrics
        print("  🔄 Creating Replica Metrics sheet...")
        if all_data['replica_metrics']:
            replica_metrics_df = pd.DataFrame(all_data['replica_metrics'])
            replica_metrics_df.to_excel(writer, sheet_name='Replica Metrics', index=False)
        else:
            # Create empty sheet with headers
            empty_replica_metrics = pd.DataFrame(columns=['metric_name', 'average', 'minimum', 'maximum', 'unit', 'source'])
            empty_replica_metrics.to_excel(writer, sheet_name='Replica Metrics', index=False)

        # Sheet 6: Replication Information
        print("  🔄 Creating Replication Information sheet...")
        if all_data['replication_info']:
            repl_data = []

            # Add header
            repl_data.append(['=== REPLICATION CONFIGURATION ===', ''])
            repl_data.append(['', ''])

            # Process replication config in a more organized way
            for key, value in all_data['replication_info'].items():
                if key == 'replica_servers' and isinstance(value, list):
                    # Handle replica servers list specially
                    if value:
                        repl_data.append(['Replica Servers', f"{len(value)} servers"])
                        for i, server in enumerate(value, 1):
                            if isinstance(server, dict):
                                server_name = server.get('name', server.get('server_name', f'Server {i}'))
                                server_status = server.get('status', server.get('state', 'Unknown'))
                                repl_data.append([f"  {i}. {server_name}", server_status])
                            else:
                                repl_data.append([f"  {i}. Server", str(server)])
                    else:
                        repl_data.append(['Replica Servers', 'None configured'])
                elif key == 'replica_servers_list':
                    # Skip this as it's a formatted version
                    continue
                elif isinstance(value, list):
                    # Handle other lists
                    if value:
                        repl_data.append([key.replace('_', ' ').title(), f"{len(value)} items"])
                        for i, item in enumerate(value, 1):
                            repl_data.append([f"  {i}. Item", str(item)])
                    else:
                        repl_data.append([key.replace('_', ' ').title(), 'None'])
                elif isinstance(value, dict):
                    # Handle nested dictionaries
                    repl_data.append([key.replace('_', ' ').title(), 'Complex Object'])
                    for sub_key, sub_value in value.items():
                        repl_data.append([f"  {sub_key.replace('_', ' ').title()}", str(sub_value)])
                else:
                    # Handle simple values
                    display_key = key.replace('_', ' ').title()
                    # Special formatting for certain keys
                    if 'lag' in key.lower() and isinstance(value, (int, float)):
                        display_value = f"{value} seconds"
                    else:
                        display_value = str(value)
                    repl_data.append([display_key, display_value])

            # Add summary section
            if len(repl_data) > 3:  # More than just headers
                repl_data.append(['', ''])
                repl_data.append(['=== SUMMARY ===', ''])

                # Count replica metrics
                replica_count = len(all_data.get('replica_metrics', []))
                repl_data.append(['Total Replica Metrics Available', replica_count])

                # Check if replication is active
                replication_enabled = all_data['replication_info'].get('replication_enabled', 'Unknown')
                repl_data.append(['Replication Status', replication_enabled])

                # Add note about raw data
                repl_data.append(['', ''])
                repl_data.append(['Note', 'See "Replication Raw" sheet for complete JSON data'])

            repl_df = pd.DataFrame(repl_data, columns=['Property', 'Value'])
            repl_df.to_excel(writer, sheet_name='Replication Info', index=False)
        else:
            # Create sheet with helpful message
            no_repl_data = [
                ['=== REPLICATION INFORMATION ===', ''],
                ['', ''],
                ['Status', 'No replication configuration found'],
                ['', ''],
                ['Note', 'This may indicate:'],
                ['', '1. Replication is not configured'],
                ['', '2. Replication files were not included in the ZIP'],
                ['', '3. Files were categorized differently'],
                ['', ''],
                ['Suggestion', 'Check "Replication Raw" sheet for any related data']
            ]
            no_repl_df = pd.DataFrame(no_repl_data, columns=['Property', 'Value'])
            no_repl_df.to_excel(writer, sheet_name='Replication Info', index=False)

        # Raw JSON Data Sheets
        print("  📄 Creating Raw JSON Data sheets...")
        sheet_count = 7
        for category, raw_data in all_data['raw_json_data'].items():
            if raw_data:
                sheet_name = category.replace('_raw', '').replace('_', ' ').title() + ' Raw'
                # Limit sheet name to 31 characters (Excel limit)
                if len(sheet_name) > 31:
                    sheet_name = sheet_name[:28] + '...'

                try:
                    raw_df = pd.DataFrame(raw_data)
                    raw_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"    ✅ Created {sheet_name} sheet with {len(raw_data)} rows")
                except Exception as e:
                    warning_msg = f"Could not create raw data sheet {sheet_name}: {e}"
                    warnings.append(warning_msg)
                    print(f"    ⚠️  {warning_msg}")

                sheet_count += 1

        # Final Sheet: Errors and Warnings
        print("  ⚠️  Creating Errors and Warnings sheet...")
        error_warning_data = []

        # Add errors
        for i, error in enumerate(errors, 1):
            error_warning_data.append(['Error', i, error, datetime.now().strftime('%Y-%m-%d %H:%M:%S')])

        # Add warnings
        for i, warning in enumerate(warnings, 1):
            error_warning_data.append(['Warning', i, warning, datetime.now().strftime('%Y-%m-%d %H:%M:%S')])

        if error_warning_data:
            error_df = pd.DataFrame(error_warning_data, columns=['Type', 'Number', 'Message', 'Timestamp'])
            error_df.to_excel(writer, sheet_name='Errors & Warnings', index=False)
        else:
            # Create empty sheet with message
            no_issues_data = [['No errors or warnings encountered during processing', '', '', '']]
            no_issues_df = pd.DataFrame(no_issues_data, columns=['Type', 'Number', 'Message', 'Timestamp'])
            no_issues_df.to_excel(writer, sheet_name='Errors & Warnings', index=False)

    print(f"✅ Excel report saved to: {output_file}")

    # Show summary
    print("\n📈 COMPREHENSIVE ANALYSIS SUMMARY:")
    print(f"  🖥️  Server: {all_data['server_info'].get('server_name', 'N/A')}")
    print(f"  📍 Location: {all_data['server_info'].get('location', 'N/A')}")
    print(f"  📊 Parameters: {len(all_data['parameters'])}")
    print(f"  📈 Primary Metrics: {len(all_data['metrics'])}")
    print(f"  🔄 Replica Metrics: {len(all_data['replica_metrics'])}")
    print(f"  🔄 Replication: {'Configured' if all_data['replication_info'] else 'Not configured'}")
    print(f"  📄 Raw JSON Files: {sum(len(files) for files in all_data['raw_json_data'].values())}")

    if all_data['metrics']:
        # Show top 5 primary metrics
        sorted_metrics = sorted(all_data['metrics'], key=lambda x: x['average'], reverse=True)[:5]
        print("\n🏆 TOP 5 PRIMARY METRICS BY AVERAGE VALUE:")
        for i, metric in enumerate(sorted_metrics, 1):
            print(f"  {i}. {metric['metric_name']}: {metric['average']} {metric['unit']}")

    if all_data['replica_metrics']:
        # Show top 5 replica metrics
        sorted_replica_metrics = sorted(all_data['replica_metrics'], key=lambda x: x['average'], reverse=True)[:5]
        print("\n🏆 TOP 5 REPLICA METRICS BY AVERAGE VALUE:")
        for i, metric in enumerate(sorted_replica_metrics, 1):
            print(f"  {i}. {metric['metric_name']}: {metric['average']} {metric['unit']}")


def main():
    """Main entry point - now uses configuration file."""
    if len(sys.argv) != 2:
        print("Azure Monitoring Data Analyzer - Enhanced Excel Edition")
        print("======================================================")
        print("")
        print("Usage: python quick_analyzer.py <config_file>")
        print("")
        print("Examples:")
        print("  python quick_analyzer.py config.json")
        print("  python quick_analyzer.py mysql_config.json")
        print("")
        print("Configuration file should contain:")
        print("  {")
        print('    "analysis": {')
        print('      "input_zip_path": "../mysql/data.zip",')
        print('      "output_excel_path": "./output/mysql_report.xlsx"')
        print("    }")
        print("  }")
        print("")
        print("Output: Comprehensive Excel file with multiple sheets:")
        print("  📋 Summary - Overview and top metrics")
        print("  🖥️  Server Information - Complete server details")
        print("  ⚙️  Parameters - All server configuration parameters")
        print("  📊 Metrics - Primary server performance metrics")
        print("  🔄 Replica Metrics - Replica server performance metrics")
        print("  🔄 Replication Info - Replica and replication details")
        print("  📄 Raw JSON Data - All original JSON files in tabular format")
        print("  ⚠️  Errors & Warnings - Processing issues and warnings")
        print("")
        sys.exit(1)

    config_file = sys.argv[1]

    try:
        # Load configuration
        config = load_config(config_file)

        # Run analysis with configuration
        success = analyze_azure_zip_with_config(config)

        if success:
            output_file = config.get('analysis', {}).get('output_excel_path', './output/analysis_report.xlsx')
            print("\n🎉 Comprehensive analysis completed successfully!")
            print(f"📊 Excel report available at: {output_file}")
            print("\n📋 Report contains:")
            print("  • Summary sheet with key information and error counts")
            print("  • Complete server configuration details")
            print("  • All server parameters and settings")
            print("  • Primary server performance metrics with statistical analysis")
            print("  • Replica server performance metrics with statistical analysis")
            print("  • Replication and replica configuration information")
            print("  • Raw JSON data from all files for custom analysis")
            print("  • Errors and warnings encountered during processing")
        else:
            print("\n⚠️  Analysis completed but encountered issues")
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ Analysis failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
