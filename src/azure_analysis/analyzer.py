#!/usr/bin/env python3
"""
Azure Monitoring Data Analyzer - Simple Working Version

This script processes Azure monitoring ZIP files and generates CSV reports.
"""

import argparse
import json
import logging
import sys
import zipfile
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

import pandas as pd
import numpy as np


class AzureMonitoringAnalyzer:
    """Main analyzer class for processing Azure monitoring data."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the analyzer with configuration."""
        self.config = self._load_config(config_path) if config_path else {}
        self.logger = self._setup_logging()
        self.temp_dir = None
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration."""
        log_level = logging.DEBUG if self.config.get('processing', {}).get('debug_mode', False) else logging.INFO
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        
        return logging.getLogger(__name__)
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            print(f"✅ Configuration loaded from {config_path}")
            return config
        except Exception as e:
            print(f"❌ Error loading config: {e}")
            return {}
    
    def extract_zip(self, zip_path: str) -> str:
        """Extract ZIP file to temporary directory."""
        print(f"📦 Extracting ZIP file: {zip_path}")
        
        if not Path(zip_path).exists():
            raise FileNotFoundError(f"ZIP file not found: {zip_path}")
        
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp(prefix="azure_analysis_")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
            
            # Find the extracted directory
            extracted_dirs = [d for d in Path(self.temp_dir).iterdir() if d.is_dir()]
            if not extracted_dirs:
                raise ValueError("No directories found in ZIP file")
            
            extracted_path = str(extracted_dirs[0])
            print(f"✅ ZIP extracted to: {extracted_path}")
            return extracted_path
            
        except Exception as e:
            print(f"❌ Error extracting ZIP: {e}")
            raise
    
    def discover_files(self, extracted_path: str) -> Dict[str, List[str]]:
        """Discover and categorize JSON files in the extracted directory."""
        print("🔍 Discovering JSON files...")
        
        files = {
            'server_details': [],
            'parameters': [],
            'metrics': [],
            'replication': [],
            'summary': [],
            'other': []
        }
        
        for json_file in Path(extracted_path).rglob("*.json"):
            file_name = json_file.name.lower()
            file_path = str(json_file)
            
            if 'server_details' in file_name or 'server_info' in file_name:
                files['server_details'].append(file_path)
            elif 'parameter' in file_name or 'config' in file_name:
                files['parameters'].append(file_path)
            elif 'replication' in file_name or 'replica' in file_name:
                files['replication'].append(file_path)
            elif 'summary' in file_name:
                files['summary'].append(file_path)
            elif any(metric in file_name for metric in ['cpu', 'memory', 'storage', 'connection', 'network', 'io']):
                files['metrics'].append(file_path)
            else:
                files['other'].append(file_path)
        
        # Log discovered files
        for category, file_list in files.items():
            if file_list:
                print(f"  📁 Found {len(file_list)} {category} files")
        
        return files
    
    def load_json_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Load and parse a JSON file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"⚠️  Error loading {file_path}: {e}")
            return None
    
    def process_metrics_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Process a metrics JSON file and extract time series data."""
        data = self.load_json_file(file_path)
        if not data:
            return None
        
        try:
            # Extract metric name from filename
            metric_name = Path(file_path).stem
            
            # Process Azure Monitor metrics format
            if 'value' in data and isinstance(data['value'], list) and len(data['value']) > 0:
                metric_data = data['value'][0]
                
                if 'timeseries' in metric_data and len(metric_data['timeseries']) > 0:
                    timeseries = metric_data['timeseries'][0]
                    
                    if 'data' in timeseries:
                        values = []
                        timestamps = []
                        
                        for point in timeseries['data']:
                            if 'average' in point and point['average'] is not None:
                                values.append(float(point['average']))
                                timestamps.append(point.get('timeStamp', ''))
                        
                        if values:
                            return {
                                'metric_name': metric_name,
                                'values': values,
                                'timestamps': timestamps,
                                'count': len(values),
                                'average': np.mean(values),
                                'min': np.min(values),
                                'max': np.max(values),
                                'std': np.std(values)
                            }
            
            return None
            
        except Exception as e:
            print(f"⚠️  Error processing metrics file {file_path}: {e}")
            return None
    
    def generate_csv_report(self, output_path: str, all_data: Dict[str, Any]) -> None:
        """Generate the final CSV report."""
        print(f"📊 Generating CSV report: {output_path}")
        
        # Ensure output directory exists
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create comprehensive report data
        report_data = []
        
        # Add server information if available
        if 'server_info' in all_data:
            server_info = all_data['server_info']
            server_row = {
                'Category': 'Server Information',
                'Metric': 'Server Details',
                'Value': f"Name: {server_info.get('server_name', 'N/A')}, "
                        f"Location: {server_info.get('location', 'N/A')}, "
                        f"Version: {server_info.get('version', 'N/A')}, "
                        f"State: {server_info.get('state', 'N/A')}",
                'Average': '',
                'Min': '',
                'Max': '',
                'Count': '',
                'Unit': 'Info'
            }
            report_data.append(server_row)
        
        # Add metrics data if available
        if 'metrics' in all_data:
            for metric in all_data['metrics']:
                metric_row = {
                    'Category': 'Performance Metrics',
                    'Metric': metric['metric_name'],
                    'Value': f"{metric['average']:.2f}",
                    'Average': metric['average'],
                    'Min': metric['min'],
                    'Max': metric['max'],
                    'Count': metric['count'],
                    'Unit': self._get_metric_unit(metric['metric_name'])
                }
                report_data.append(metric_row)
        
        # Create DataFrame and save to CSV
        if report_data:
            report_df = pd.DataFrame(report_data)
            report_df.to_csv(output_path, index=False)
            print(f"✅ CSV report saved to: {output_path}")
            print(f"📈 Report contains {len(report_data)} rows")
        else:
            print("⚠️  No data to write to CSV")
    
    def _get_metric_unit(self, metric_name: str) -> str:
        """Get the appropriate unit for a metric based on its name."""
        metric_name_lower = metric_name.lower()
        
        if 'percent' in metric_name_lower or 'cpu' in metric_name_lower:
            return '%'
        elif 'bytes' in metric_name_lower or 'storage' in metric_name_lower:
            return 'Bytes'
        elif 'connection' in metric_name_lower:
            return 'Count'
        elif 'iops' in metric_name_lower:
            return 'IOPS'
        elif 'throughput' in metric_name_lower:
            return 'Bytes/sec'
        elif 'time' in metric_name_lower or 'delay' in metric_name_lower:
            return 'Seconds'
        else:
            return 'Value'
    
    def analyze(self, zip_path: str, output_csv_path: str) -> None:
        """Main analysis method that orchestrates the entire process."""
        try:
            print("🚀 Starting Azure Monitoring Data Analysis")
            
            # Step 1: Extract ZIP file
            extracted_path = self.extract_zip(zip_path)
            
            # Step 2: Discover files
            files = self.discover_files(extracted_path)
            
            # Step 3: Process data
            all_data = {}
            
            # Process metrics
            if files['metrics']:
                print(f"📊 Processing {len(files['metrics'])} metrics files...")
                metrics = []
                for file_path in files['metrics']:
                    metric_data = self.process_metrics_file(file_path)
                    if metric_data:
                        metrics.append(metric_data)
                all_data['metrics'] = metrics
                print(f"✅ Processed {len(metrics)} valid metrics")
            
            # Process server details
            if files['server_details']:
                print(f"🖥️  Processing server details...")
                server_info = {}
                for file_path in files['server_details']:
                    data = self.load_json_file(file_path)
                    if data and isinstance(data, dict):
                        server_info.update({
                            'server_name': data.get('name', ''),
                            'resource_group': data.get('resourceGroup', ''),
                            'location': data.get('location', ''),
                            'version': data.get('version', ''),
                            'state': data.get('state', ''),
                        })
                all_data['server_info'] = server_info
            
            # Step 4: Generate CSV report
            self.generate_csv_report(output_csv_path, all_data)
            
            print("🎉 Analysis completed successfully!")
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            raise
        finally:
            # Cleanup temporary directory
            if self.temp_dir and Path(self.temp_dir).exists():
                shutil.rmtree(self.temp_dir)
                print("🧹 Temporary files cleaned up")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description="Azure Monitoring Data Analyzer")
    
    parser.add_argument('--config', '-c', type=str, help='Path to configuration JSON file')
    parser.add_argument('--input-zip', '-i', type=str, help='Path to input ZIP file')
    parser.add_argument('--output-csv', '-o', type=str, help='Path for output CSV report')
    parser.add_argument('--help-extended', action='store_true', help='Show extended help')
    
    args = parser.parse_args()
    
    if args.help_extended:
        print("""
Azure Monitoring Data Analyzer
==============================

This tool processes Azure monitoring ZIP files and generates CSV reports.

Examples:
  # Using configuration file
  python -m azure_analysis --config config.json
  
  # Direct parameters  
  python -m azure_analysis --input-zip data.zip --output-csv report.csv
        """)
        return
    
    # Validate arguments
    if args.config:
        analyzer = AzureMonitoringAnalyzer(args.config)
        zip_path = analyzer.config.get('analysis', {}).get('input_zip_path')
        output_path = analyzer.config.get('analysis', {}).get('output_csv_path')
        
        if not zip_path or not output_path:
            print("❌ Configuration file must specify input_zip_path and output_csv_path")
            sys.exit(1)
            
    elif args.input_zip and args.output_csv:
        analyzer = AzureMonitoringAnalyzer()
        zip_path = args.input_zip
        output_path = args.output_csv
        
    else:
        print("❌ Either --config or both --input-zip and --output-csv must be specified")
        parser.print_help()
        sys.exit(1)
    
    # Run analysis
    try:
        analyzer.analyze(zip_path, output_path)
        print(f"\n✅ Analysis completed successfully!")
        print(f"📊 CSV report saved to: {output_path}")
        
    except Exception as e:
        print(f"\n❌ Analysis failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
