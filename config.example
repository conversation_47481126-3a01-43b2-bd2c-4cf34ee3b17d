# Azure Configuration File
# Copy this file and update with your actual values

# Azure Subscription ID
AZURE_SUBSCRIPTION_ID="207a84e2-208b-4809-baab-e94a043914f5"

# Resource Group Name
RESOURCE_GROUP_NAME="amit-test"

# MySQL Flexible Server Name
SERVER_NAME="amit-mysql-server"

# Monitoring Configuration
# METRICS_TIME_PERIOD: Time period for metrics collection
# Supported values: 1h, 6h, 12h, 1d, 3d, 7d, 30d
# Examples: "1h" = 1 hour, "1d" = 1 day, "7d" = 7 days
METRICS_TIME_PERIOD="1h"

# Server Parameters Configuration
# FETCH_PARAMETERS: Whether to fetch MySQL server parameters
# Set to "true" to fetch server parameters, "false" to skip
# Default: false (to keep script execution faster)
FETCH_PARAMETERS="false"
